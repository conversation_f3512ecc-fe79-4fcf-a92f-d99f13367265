#!/usr/bin/env python3
"""
百度音频转写工具包使用示例
演示三种不同的转写方案
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from audio_transcription import (
    BaiduTopSpeedTranscription,
    BaiduStandardTranscription,
    BaiduFileTranscription
)


def test_all_transcription_methods():
    """测试所有转写方法"""
    
    # API配置
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv("BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo")
    
    # 测试音频文件
    audio_file_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"
    
    if not os.path.exists(audio_file_path):
        print(f"❌ 测试音频文件不存在: {audio_file_path}")
        return
    
    print("🎵 百度音频转写工具包测试")
    print("=" * 60)
    
    # 方案1：极速版API（推荐，无需切分）
    print("\n🚀 方案1：极速版API（推荐）")
    print("-" * 40)
    try:
        client1 = BaiduTopSpeedTranscription(api_key, secret_key)
        result1 = client1.transcribe_local_file(
            audio_file_path=audio_file_path,
            enable_subtitle=1,
            smooth_text=1,
            smooth_text_param=[1, 2, 3]
        )
        
        if result1["success"]:
            print("✅ 极速版API成功")
            full_text = " ".join(result1.get("result", []))
            print(f"📝 结果: {full_text[:100]}...")
            client1.save_result_to_file(result1, "极速版结果.json")
        else:
            print(f"❌ 极速版API失败: {result1['error']}")
            if result1.get("error_code") == -5000:
                print("💡 提示：API额度不足，请检查控制台或领取免费额度")
                
    except Exception as e:
        print(f"❌ 极速版API异常: {e}")
    
    # 方案2：标准版API（自动分割）
    print("\n🔧 方案2：标准版API（自动分割）")
    print("-" * 40)
    try:
        client2 = BaiduStandardTranscription(api_key, secret_key)
        result2 = client2.transcribe_local_file(audio_file_path)
        
        if result2["success"]:
            print("✅ 标准版API成功")
            print(f"📝 结果: {result2['text'][:100]}...")
            print(f"📊 分段数: {result2.get('total_chunks', 0)}")
            client2.save_result_to_file(result2, "标准版结果.json")
        else:
            print(f"❌ 标准版API失败: {result2['error']}")
            
    except Exception as e:
        print(f"❌ 标准版API异常: {e}")
    
    # 方案3：异步文件转写API（需要URL）
    print("\n⏳ 方案3：异步文件转写API")
    print("-" * 40)
    print("💡 此方案需要音频文件的公网URL，跳过本地文件测试")
    print("   如需使用，请先上传文件到云存储获取URL")
    
    # 使用示例URL测试
    try:
        client3 = BaiduFileTranscription(api_key, secret_key)
        test_url = "https://platform.bj.bcebos.com/sdk/asr/asr_doc/doc_download_files/16k.wav"
        
        print(f"🔗 测试URL: {test_url}")
        result3 = client3.transcribe_audio_url(
            speech_url=test_url,
            format="wav",
            pid=80001,
            wait_for_result=True
        )
        
        if result3["success"]:
            print("✅ 异步文件转写API成功")
            print(f"📝 结果: {' '.join(result3.get('result', []))}")
        else:
            print(f"❌ 异步文件转写API失败: {result3['error']}")
            
    except Exception as e:
        print(f"❌ 异步文件转写API异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 推荐使用顺序：")
    print("1. 极速版API（最佳，无需切分，但需要额度）")
    print("2. 标准版API（备选，自动分割处理）")
    print("3. 异步文件转写API（需要云存储URL）")
    print("=" * 60)


def show_usage_guide():
    """显示使用指南"""
    print("""
🎵 百度音频转写工具包使用指南
================================

📦 安装依赖：
pip install requests

🔑 环境变量设置：
export BAIDU_API_KEY='你的API_Key'
export BAIDU_SECRET_KEY='你的Secret_Key'

📋 三种转写方案：

1️⃣ 极速版API（推荐）
   - 优势：无需切分，支持1小时/500MB，极速返回
   - 限制：需要API额度
   - 适用：实时转写，大文件处理

2️⃣ 标准版API（备选）
   - 优势：自动分割处理，稳定可靠
   - 限制：需要分割大文件
   - 适用：额度不足时的备选方案

3️⃣ 异步文件转写API
   - 优势：支持超大文件，异步处理
   - 限制：需要公网URL
   - 适用：批量处理，超大文件

💡 使用建议：
- 优先使用极速版API
- 极速版失败时自动切换到标准版
- 需要处理超大文件时使用异步API

🔗 相关链接：
- 控制台：https://console.bce.baidu.com/
- 免费额度：https://cloud.baidu.com/doc/SPEECH/s/Wl9mh4doe
- API文档：https://cloud.baidu.com/doc/SPEECH/s/Clhohwkbv
""")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="百度音频转写工具包")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--guide", action="store_true", help="显示使用指南")
    
    args = parser.parse_args()
    
    if args.test:
        test_all_transcription_methods()
    elif args.guide:
        show_usage_guide()
    else:
        print("使用 --test 运行测试，或 --guide 查看使用指南")
