# 百度音频文件转写API使用指南

## 概述

本项目提供了完整的百度音频文件转写解决方案，支持大文件异步转写，无需手动分割音频。

## 主要优势

✅ **支持大文件**：音频大小不超过500MB  
✅ **无需分割**：可以直接处理长音频文件  
✅ **异步处理**：提交任务后异步处理，12小时内返回结果  
✅ **详细结果**：提供分段转写详细结果，包含时间戳  
✅ **多种格式**：支持mp3、wav、pcm、m4a、amr格式  

## 文件说明

### 核心文件

1. **`baidu_audio_transcription.py`** - 基础音频转写API客户端
2. **`complete_audio_transcription.py`** - 完整解决方案（包含模拟上传）
3. **`bos_audio_transcription.py`** - 使用百度云对象存储BOS的完整方案

### 测试文件

- **`test_baidu_speech.py`** - 原始的短语音识别API（已弃用）

## 快速开始

### 1. 安装依赖

```bash
pip install requests
# 如果使用BOS存储，还需要：
pip install bce-python-sdk
```

### 2. 设置环境变量

```bash
export BAIDU_API_KEY='你的API_Key'
export BAIDU_SECRET_KEY='你的Secret_Key'

# 如果使用BOS存储：
export BOS_ACCESS_KEY='你的BOS_Access_Key'
export BOS_SECRET_KEY='你的BOS_Secret_Key'
export BOS_BUCKET_NAME='你的存储桶名称'
```

### 3. 基础使用

```python
from baidu_audio_transcription import BaiduAudioTranscription

# 初始化客户端
client = BaiduAudioTranscription(api_key, secret_key)

# 转写网络音频文件
result = client.transcribe_audio_url(
    speech_url="https://example.com/audio.wav",
    format="wav",
    pid=80001,  # 中文极速版
    smooth_text=1,  # 开启文本顺滑
    wait_for_result=True
)

if result["success"]:
    print(f"转写结果: {result['result']}")
    print(f"音频时长: {result['audio_duration']/1000:.2f} 秒")
else:
    print(f"转写失败: {result['error']}")
```

### 4. 本地文件转写（推荐）

```python
from bos_audio_transcription import BOSAudioTranscription

# 初始化客户端（需要配置BOS）
client = BOSAudioTranscription(
    api_key=api_key,
    secret_key=secret_key,
    bos_access_key=bos_access_key,
    bos_secret_key=bos_secret_key,
    bucket_name="your-bucket-name"
)

# 转写本地音频文件
result = client.transcribe_local_file(
    local_file_path="/path/to/your/audio.wav",
    pid=80001,
    smooth_text=1,
    wait_for_result=True,
    cleanup_after=True  # 转写完成后删除云端文件
)
```

## API参数说明

### 语言类型 (pid)

- `80001` - 中文语音近场识别模型极速版（推荐）
- `80006` - 中文音视频字幕模型
- `1737` - 英文模型

### 音频格式 (format)

- `wav` - WAV格式（推荐）
- `mp3` - MP3格式
- `pcm` - PCM格式
- `m4a` - M4A格式
- `amr` - AMR格式

### 其他参数

- `smooth_text` - 文本顺滑：0（不开启），1（开启）
- `filter_sensitive` - 敏感词过滤：0（不开启），1（开启）
- `rate` - 采样率：16000（固定值）

## 云存储配置

### 百度云对象存储BOS（推荐）

1. 注册百度智能云账号：https://cloud.baidu.com/
2. 开通对象存储BOS服务：https://cloud.baidu.com/product/bos.html
3. 创建存储桶（Bucket）
4. 设置存储桶为公共读权限
5. 获取Access Key和Secret Key

### 其他云存储

也可以使用其他云存储服务：
- 阿里云对象存储OSS
- 腾讯云对象存储COS
- AWS S3

只需修改 `upload_to_temp_storage` 方法实现对应的上传逻辑。

## 错误处理

常见错误码：

- `336203` - 缺少参数
- `3301` - 音频质量错误
- `3300` - token错误
- `3310` - 内容长度过长

## 注意事项

1. **文件大小限制**：单个音频文件不超过500MB
2. **处理时间**：异步处理，一般12小时内返回结果
3. **费用**：按音频时长计费，详见百度智能云定价
4. **存储费用**：使用云存储会产生额外的存储和流量费用
5. **清理文件**：建议转写完成后及时删除云端临时文件

## 批量处理

```python
# 批量转写多个文件
file_paths = [
    "/path/to/audio1.wav",
    "/path/to/audio2.mp3",
    "/path/to/audio3.m4a"
]

results = client.batch_transcribe_files(
    file_paths=file_paths,
    pid=80001,
    smooth_text=1
)

for result in results:
    if result["success"]:
        print(f"文件 {result['file_path']} 转写成功")
        print(f"结果: {result['result']}")
    else:
        print(f"文件 {result['file_path']} 转写失败: {result['error']}")
```

## 性能优化建议

1. **音频预处理**：
   - 使用16kHz采样率
   - 单声道音频
   - 适当的音频质量

2. **批量处理**：
   - 合理控制并发数量
   - 避免频繁的API调用

3. **存储优化**：
   - 及时清理临时文件
   - 设置生命周期规则

## 技术支持

如有问题，请参考：
- 百度智能云语音识别文档：https://cloud.baidu.com/doc/SPEECH/s/Klbxern8v
- 百度智能云对象存储文档：https://cloud.baidu.com/doc/BOS/index.html

## 更新日志

- v1.0 - 初始版本，支持基础音频转写
- v1.1 - 添加BOS存储支持
- v1.2 - 添加批量处理功能
