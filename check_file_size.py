#!/usr/bin/env python3
import os

file_path = '/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav'
if os.path.exists(file_path):
    size = os.path.getsize(file_path)
    print(f'文件大小: {size} 字节 ({size/1024/1024:.2f} MB)')
    
    # 百度语音识别极速版限制是60秒，大约10MB
    if size > 10 * 1024 * 1024:
        print('警告：文件超过10MB，可能会被拒绝')
    else:
        print('文件大小在允许范围内')
else:
    print('文件不存在')
