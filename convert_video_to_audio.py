#!/usr/bin/env python3
"""
视频转音频脚本
将视频文件转换为百度语音识别API支持的音频格式
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional, Tuple


class VideoToAudioConverter:
    """视频转音频转换器"""
    
    def __init__(self):
        self.supported_video_formats = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.mpg', '.mpeg']
        self.supported_audio_formats = ['.wav', '.mp3', '.m4a', '.flac']
        
    def check_ffmpeg(self) -> bool:
        """检查是否安装了ffmpeg"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def get_video_info(self, video_path: str) -> dict:
        """获取视频文件信息"""
        if not self.check_ffmpeg():
            return {}
            
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            import json
            return json.loads(result.stdout)
        except subprocess.CalledProcessError:
            return {}
    
    def convert_to_wav(self, 
                      input_path: str, 
                      output_path: Optional[str] = None,
                      sample_rate: int = 16000,
                      channels: int = 1,
                      remove_original: bool = False) -> Tuple[bool, str]:
        """
        将视频转换为WAV格式
        
        Args:
            input_path: 输入视频文件路径
            output_path: 输出音频文件路径，如果为None则自动生成
            sample_rate: 采样率，默认16000Hz
            channels: 声道数，默认1（单声道）
            remove_original: 是否删除原文件
            
        Returns:
            (成功标志, 输出文件路径或错误信息)
        """
        if not os.path.exists(input_path):
            return False, f"文件不存在: {input_path}"
            
        # 检查输入文件格式
        input_path = Path(input_path)
        if input_path.suffix.lower() not in self.supported_video_formats + self.supported_audio_formats:
            return False, f"不支持的文件格式: {input_path.suffix}"
        
        # 自动生成输出路径
        if output_path is None:
            output_path = input_path.with_suffix('.wav')
        else:
            output_path = Path(output_path)
            
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not self.check_ffmpeg():
            return False, "未找到ffmpeg，请先安装: brew install ffmpeg"
        
        try:
            # 构建ffmpeg命令
            cmd = [
                'ffmpeg', '-i', str(input_path),
                '-ar', str(sample_rate),      # 采样率
                '-ac', str(channels),         # 声道数
                '-sample_fmt', 's16',         # 采样格式
                '-y',                         # 覆盖输出文件
                str(output_path)
            ]
            
            # 执行转换
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            if remove_original and str(input_path) != str(output_path):
                os.remove(input_path)
                
            return True, str(output_path)
            
        except subprocess.CalledProcessError as e:
            return False, f"转换失败: {e.stderr}"
        except Exception as e:
            return False, f"发生错误: {str(e)}"
    
    def convert_directory(self, 
                         input_dir: str, 
                         output_dir: str,
                         sample_rate: int = 16000,
                         channels: int = 1) -> dict:
        """
        批量转换目录中的视频文件
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径
            sample_rate: 采样率
            channels: 声道数
            
        Returns:
            包含转换结果的字典
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            return {"error": f"目录不存在: {input_dir}"}
        
        results = {
            "total_files": 0,
            "successful": 0,
            "failed": 0,
            "files": []
        }
        
        # 查找所有视频文件
        for video_ext in self.supported_video_formats:
            for video_file in input_path.rglob(f"*{video_ext}"):
                results["total_files"] += 1
                
                # 构建输出路径
                relative_path = video_file.relative_to(input_path)
                output_file = output_path / relative_path.with_suffix('.wav')
                output_file.parent.mkdir(parents=True, exist_ok=True)
                
                success, message = self.convert_to_wav(
                    str(video_file),
                    str(output_file),
                    sample_rate,
                    channels
                )
                
                file_result = {
                    "input": str(video_file),
                    "output": str(output_file) if success else None,
                    "success": success,
                    "message": message
                }
                
                results["files"].append(file_result)
                
                if success:
                    results["successful"] += 1
                else:
                    results["failed"] += 1
        
        return results


def main():
    """主函数"""
    converter = VideoToAudioConverter()
    
    if not converter.check_ffmpeg():
        print("错误：未找到ffmpeg")
        print("请使用以下命令安装：")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu/Debian: sudo apt-get install ffmpeg")
        print("  Windows: 下载并安装 https://ffmpeg.org/download.html")
        sys.exit(1)
    
    # 设置输入输出路径
    input_video = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.mp4"
    output_audio = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"
    
    if not os.path.exists(input_video):
        print(f"错误：文件不存在: {input_video}")
        sys.exit(1)
    
    print(f"正在转换: {input_video}")
    print(f"输出路径: {output_audio}")
    
    # 执行转换
    success, message = converter.convert_to_wav(
        input_video,
        output_audio,
        sample_rate=16000,
        channels=1
    )
    
    if success:
        print("✅ 转换成功!")
        print(f"输出文件: {message}")
        
        # 显示文件信息
        if os.path.exists(message):
            file_size = os.path.getsize(message)
            print(f"文件大小: {file_size / 1024 / 1024:.2f} MB")
    else:
        print(f"❌ 转换失败: {message}")


if __name__ == "__main__":
    main()