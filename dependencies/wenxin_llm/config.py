#!/usr/bin/env python3
"""
文心大模型配置文件
"""

import os
from typing import Dict, Any


class WenxinConfig:
    """文心大模型配置类"""
    
    # 默认配置
    DEFAULT_API_KEY = "450803ef6babe9f237b3e393230d3f8c6645b7fe"
    DEFAULT_BASE_URL = "https://aistudio.baidu.com/llm/lmapi/v3"
    DEFAULT_MODEL = "ernie-4.5-21b-a3b"
    DEFAULT_MODEL_PROVIDER = "openai"
    
    # 可用模型列表
    AVAILABLE_MODELS = {
        "ernie-4.5-21b-a3b": {
            "name": "ERNIE 4.5 21B A3B",
            "description": "文心大模型4.5版本，21B参数",
            "max_tokens": 8192,
            "supports_streaming": True
        },
        "ernie-4.0-8k": {
            "name": "ERNIE 4.0 8K",
            "description": "文心大模型4.0版本，支持8K上下文",
            "max_tokens": 8192,
            "supports_streaming": True
        },
        "ernie-3.5": {
            "name": "ERNIE 3.5",
            "description": "文心大模型3.5版本",
            "max_tokens": 4096,
            "supports_streaming": True
        }
    }
    
    @classmethod
    def get_api_credentials(cls) -> tuple:
        """获取API凭证"""
        api_key = os.getenv("WENXIN_API_KEY", cls.DEFAULT_API_KEY)
        base_url = os.getenv("WENXIN_BASE_URL", cls.DEFAULT_BASE_URL)
        return api_key, base_url
    
    @classmethod
    def get_default_config(cls, model: str = None) -> Dict[str, Any]:
        """获取默认配置"""
        api_key, base_url = cls.get_api_credentials()
        
        config = {
            "model": model or cls.DEFAULT_MODEL,
            "base_url": base_url,
            "api_key": api_key,
            "model_provider": cls.DEFAULT_MODEL_PROVIDER,
        }
        
        return config
    
    @classmethod
    def get_model_info(cls, model: str) -> Dict[str, Any]:
        """获取模型信息"""
        return cls.AVAILABLE_MODELS.get(model, {})
    
    @classmethod
    def list_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """列出所有可用模型"""
        return cls.AVAILABLE_MODELS
    
    @classmethod
    def validate_model(cls, model: str) -> bool:
        """验证模型是否可用"""
        return model in cls.AVAILABLE_MODELS


# 使用示例
if __name__ == "__main__":
    config = WenxinConfig()
    
    print("API凭证:", config.get_api_credentials())
    print("默认配置:", config.get_default_config())
    print("可用模型:", list(config.list_available_models().keys()))
    print("模型信息:", config.get_model_info("ernie-4.5-21b-a3b"))
