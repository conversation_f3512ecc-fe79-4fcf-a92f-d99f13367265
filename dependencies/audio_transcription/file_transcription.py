#!/usr/bin/env python3
"""
百度音频文件转写标准版API客户端
基于百度智能云音频文件转写API文档：https://cloud.baidu.com/doc/SPEECH/s/Klbxern8v
支持大文件异步转写，无需分割音频
特点：异步处理，支持超大文件，需要公网URL
"""

import json
import os
import time
from typing import Any, Dict, List

import requests


class BaiduAudioFileStandardTranscription:
    """百度音频文件转写标准版API客户端"""

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化音频转写客户端

        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None

        # API端点
        self.create_url = "https://aip.baidubce.com/rpc/2.0/aasr/v1/create"
        self.query_url = "https://aip.baidubce.com/rpc/2.0/aasr/v1/query"

    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token:
            return self.access_token

        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key,
        }

        try:
            response = requests.post(url, params=params)
            response.raise_for_status()
            result = response.json()

            if "access_token" in result:
                self.access_token = result["access_token"]
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {e}")

    def create_transcription_task(
        self,
        speech_url: str,
        format: str = "wav",
        pid: int = 80001,
        rate: int = 16000,
        smooth_text: int = 0,
        filter_sensitive: int = 0,
    ) -> Dict[str, Any]:
        """
        创建音频转写任务

        Args:
            speech_url: 音频文件URL（可使用百度云对象存储）
            format: 音频格式 ["mp3", "wav", "pcm", "m4a", "amr"]
            pid: 语言类型 80001（中文极速版）, 80006（中文音视频字幕模型）, 1737（英文模型）
            rate: 采样率 [16000] 固定值
            smooth_text: 文本顺滑 0（不开启）, 1（开启）
            filter_sensitive: 敏感词过滤 0（不开启）, 1（开启）

        Returns:
            创建结果字典，包含task_id
        """
        access_token = self.get_access_token()

        url = f"{self.create_url}?access_token={access_token}"

        data = {"speech_url": speech_url, "format": format, "pid": pid, "rate": rate}

        # 可选参数
        if smooth_text:
            data["smooth_text"] = smooth_text
        if filter_sensitive:
            data["filter_sensitive"] = filter_sensitive

        headers = {"Content-Type": "application/json", "Accept": "application/json"}

        try:
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()
            result = response.json()

            if "task_id" in result:
                return {
                    "success": True,
                    "task_id": result["task_id"],
                    "task_status": result.get("task_status", "Created"),
                    "log_id": result.get("log_id"),
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error_msg", "创建任务失败"),
                    "error_code": result.get("error_code"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {e}", "error_code": -1}
        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -2}

    def query_transcription_tasks(self, task_ids: List[str]) -> Dict[str, Any]:
        """
        查询音频转写任务结果

        Args:
            task_ids: 任务ID列表（最多200个）

        Returns:
            查询结果字典
        """
        if not task_ids:
            return {"success": False, "error": "task_ids不能为空", "error_code": -3}

        if len(task_ids) > 200:
            return {
                "success": False,
                "error": "单次查询任务数不能超过200个",
                "error_code": -4,
            }

        access_token = self.get_access_token()

        url = f"{self.query_url}?access_token={access_token}"

        data = {"task_ids": task_ids}

        headers = {"Content-Type": "application/json", "Accept": "application/json"}

        try:
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()
            result = response.json()

            if "tasks_info" in result:
                return {
                    "success": True,
                    "tasks_info": result["tasks_info"],
                    "log_id": result.get("log_id"),
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error_msg", "查询任务失败"),
                    "error_code": result.get("error_code"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {e}", "error_code": -1}
        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -2}

    def wait_for_completion(
        self, task_id: str, max_wait_time: int = 3600, check_interval: int = 30
    ) -> Dict[str, Any]:
        """
        等待任务完成

        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒），默认1小时
            check_interval: 检查间隔（秒），默认30秒

        Returns:
            最终结果字典
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            result = self.query_transcription_tasks([task_id])

            if not result["success"]:
                return result

            tasks_info = result["tasks_info"]
            if not tasks_info:
                return {"success": False, "error": "未找到任务信息", "error_code": -5}

            task_info = tasks_info[0]
            task_status = task_info.get("task_status")

            print(f"任务状态: {task_status}")

            if task_status == "Success":
                return {
                    "success": True,
                    "task_info": task_info,
                    "result": task_info.get("task_result", {}).get("result", []),
                    "detailed_result": task_info.get("task_result", {}).get(
                        "detailed_result", []
                    ),
                    "audio_duration": task_info.get("task_result", {}).get(
                        "audio_duration"
                    ),
                }
            elif task_status == "Failure":
                task_result = task_info.get("task_result", {})
                return {
                    "success": False,
                    "error": task_result.get("err_msg", "转写失败"),
                    "error_code": task_result.get("err_no"),
                    "task_info": task_info,
                }
            elif task_status in ["Created", "Running"]:
                print(f"等待 {check_interval} 秒后重新检查...")
                time.sleep(check_interval)
            else:
                return {
                    "success": False,
                    "error": f"未知任务状态: {task_status}",
                    "error_code": -6,
                    "task_info": task_info,
                }

        return {
            "success": False,
            "error": f"等待超时（{max_wait_time}秒）",
            "error_code": -7,
        }

    def transcribe_audio_url(
        self,
        speech_url: str,
        format: str = "wav",
        wait_for_result: bool = True,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        一站式音频转写（创建任务并等待结果）

        Args:
            speech_url: 音频文件URL
            format: 音频格式
            wait_for_result: 是否等待结果
            **kwargs: 其他参数传递给create_transcription_task

        Returns:
            转写结果字典
        """
        print("正在创建转写任务...")
        print(f"音频URL: {speech_url}")

        # 创建任务
        create_result = self.create_transcription_task(
            speech_url=speech_url, format=format, **kwargs
        )

        if not create_result["success"]:
            return create_result

        task_id = create_result["task_id"]
        print(f"任务创建成功，task_id: {task_id}")

        if not wait_for_result:
            return create_result

        # 等待完成
        print("正在等待转写完成...")
        return self.wait_for_completion(task_id)


def test_transcription():
    """测试音频转写功能"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv(
        "BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo"
    )

    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return

    # 创建转写客户端
    client = BaiduAudioFileStandardTranscription(api_key, secret_key)

    # 测试音频URL（需要是公网可访问的URL）
    # 注意：这里需要替换为你的音频文件URL
    test_audio_url = (
        "https://platform.bj.bcebos.com/sdk/asr/asr_doc/doc_download_files/16k.wav"
    )

    print("开始音频转写测试...")

    # 执行转写
    result = client.transcribe_audio_url(
        speech_url=test_audio_url,
        format="wav",
        pid=80001,  # 中文极速版
        smooth_text=1,  # 开启文本顺滑
        wait_for_result=True,
    )

    if result["success"]:
        print("\n" + "=" * 50)
        print("转写成功!")
        print(f"转写结果: {result['result']}")
        if result.get("audio_duration"):
            print(f"音频时长: {result['audio_duration'] / 1000:.2f} 秒")

        # 显示详细分段结果
        if result.get("detailed_result"):
            print("\n分段详细结果:")
            for i, segment in enumerate(result["detailed_result"]):
                start_time = segment.get("begin_time", 0) / 1000
                end_time = segment.get("end_time", 0) / 1000
                text = " ".join(segment.get("res", []))
                print(f"  [{start_time:.2f}s - {end_time:.2f}s]: {text}")

        print("=" * 50)
    else:
        print(f"\n转写失败: {result['error']} (错误码: {result.get('error_code')})")


if __name__ == "__main__":
    test_transcription()
