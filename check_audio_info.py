#!/usr/bin/env python3
import os
import wave

file_path = '/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav'

if os.path.exists(file_path):
    size = os.path.getsize(file_path)
    print(f'文件大小: {size} 字节 ({size/1024/1024:.2f} MB)')
    
    try:
        with wave.open(file_path, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            duration = frames / float(sample_rate)
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            
            print(f'音频时长: {duration:.2f} 秒')
            print(f'采样率: {sample_rate} Hz')
            print(f'声道数: {channels}')
            print(f'采样位深: {sample_width * 8} bit')
            
            if duration > 60:
                print('警告：音频时长超过60秒，极速版API不支持')
            else:
                print('音频时长在允许范围内')
                
    except Exception as e:
        print(f'无法读取音频文件信息: {e}')
else:
    print('文件不存在')
