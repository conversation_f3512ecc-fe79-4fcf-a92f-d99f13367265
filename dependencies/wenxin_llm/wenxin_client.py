#!/usr/bin/env python3
"""
文心大模型客户端
基于LangChain的文心大模型封装
"""

import os
from typing import Any, Dict, List, Optional, Union

from .config import WenxinConfig

try:
    from langchain.chat_models import init_chat_model
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class WenxinLLMClient:
    """文心大模型客户端"""
    
    def __init__(
        self, 
        model: str = None,
        api_key: str = None,
        base_url: str = None,
        **kwargs
    ):
        """
        初始化文心大模型客户端
        
        Args:
            model: 模型名称
            api_key: API密钥
            base_url: API基础URL
            **kwargs: 其他配置参数
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("需要安装langchain: pip install langchain")
        
        # 获取配置
        self.config = WenxinConfig.get_default_config(model)
        
        # 覆盖配置
        if api_key:
            self.config["api_key"] = api_key
        if base_url:
            self.config["base_url"] = base_url
        
        # 添加其他参数
        self.config.update(kwargs)
        
        # 验证模型
        if not WenxinConfig.validate_model(self.config["model"]):
            available_models = list(WenxinConfig.list_available_models().keys())
            raise ValueError(f"不支持的模型: {self.config['model']}，可用模型: {available_models}")
        
        # 初始化LLM
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self):
        """初始化LLM实例"""
        try:
            self.llm = init_chat_model(**self.config)
            print(f"✅ 文心大模型初始化成功: {self.config['model']}")
        except Exception as e:
            print(f"❌ 文心大模型初始化失败: {e}")
            raise
    
    def chat(self, message: str, **kwargs) -> str:
        """
        单轮对话
        
        Args:
            message: 用户消息
            **kwargs: 其他参数
            
        Returns:
            模型回复
        """
        if not self.llm:
            raise RuntimeError("LLM未初始化")
        
        try:
            response = self.llm.invoke(message, **kwargs)
            return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            print(f"❌ 对话失败: {e}")
            raise
    
    def batch_chat(self, messages: List[str], **kwargs) -> List[str]:
        """
        批量对话
        
        Args:
            messages: 用户消息列表
            **kwargs: 其他参数
            
        Returns:
            模型回复列表
        """
        if not self.llm:
            raise RuntimeError("LLM未初始化")
        
        results = []
        for i, message in enumerate(messages):
            try:
                print(f"处理第 {i+1}/{len(messages)} 条消息...")
                response = self.chat(message, **kwargs)
                results.append(response)
            except Exception as e:
                print(f"第 {i+1} 条消息处理失败: {e}")
                results.append(f"错误: {e}")
        
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        return WenxinConfig.get_model_info(self.config["model"])
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config.copy()
    
    def switch_model(self, model: str):
        """
        切换模型
        
        Args:
            model: 新的模型名称
        """
        if not WenxinConfig.validate_model(model):
            available_models = list(WenxinConfig.list_available_models().keys())
            raise ValueError(f"不支持的模型: {model}，可用模型: {available_models}")
        
        self.config["model"] = model
        self._initialize_llm()
        print(f"✅ 已切换到模型: {model}")


def test_wenxin_client():
    """测试文心大模型客户端"""
    print("🤖 文心大模型客户端测试")
    print("=" * 50)
    
    try:
        # 初始化客户端
        client = WenxinLLMClient()
        
        # 显示模型信息
        model_info = client.get_model_info()
        print(f"📋 当前模型: {model_info.get('name', '未知')}")
        print(f"📝 模型描述: {model_info.get('description', '无描述')}")
        print(f"🔢 最大tokens: {model_info.get('max_tokens', '未知')}")
        
        # 单轮对话测试
        print(f"\n💬 单轮对话测试:")
        test_message = "你好，请简单介绍一下你自己"
        print(f"用户: {test_message}")
        
        response = client.chat(test_message)
        print(f"助手: {response}")
        
        # 批量对话测试
        print(f"\n📝 批量对话测试:")
        test_messages = [
            "1+1等于几？",
            "请用一句话解释什么是人工智能",
            "今天天气怎么样？"
        ]
        
        responses = client.batch_chat(test_messages)
        for i, (msg, resp) in enumerate(zip(test_messages, responses)):
            print(f"问题{i+1}: {msg}")
            print(f"回答{i+1}: {resp[:100]}..." if len(resp) > 100 else f"回答{i+1}: {resp}")
            print()
        
        print("✅ 测试完成！")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请安装依赖: pip install langchain")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_wenxin_client()
