#!/usr/bin/env python3
"""
音频转写工具包使用示例
使用整理后的dependencies目录中的工具
"""

import os
import sys

# 添加dependencies目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), "dependencies"))

from audio_transcription.config import TranscriptionConfig


def smart_transcription(audio_file_path: str, output_file: str = None):
    """
    智能音频转写：自动选择最佳转写方案

    Args:
        audio_file_path: 音频文件路径
        output_file: 输出文件路径（可选）

    Returns:
        转写结果字典
    """
    print(f"🎵 开始智能音频转写: {os.path.basename(audio_file_path)}")
    print("=" * 60)

    # 获取API凭证
    api_key, secret_key = TranscriptionConfig.get_api_credentials()

    # 验证文件
    is_valid, error_msg = TranscriptionConfig.validate_file(audio_file_path, "topspeed")
    if not is_valid:
        print(f"❌ 文件验证失败: {error_msg}")
        return {"success": False, "error": error_msg}

    # 方案1：尝试音频文件转写极速版API（推荐）
    print("\n🚀 尝试音频文件转写极速版API...")
    try:
        topspeed_client = BaiduAudioFileTopSpeedTranscription(api_key, secret_key)
        result = topspeed_client.transcribe_local_file(
            audio_file_path=audio_file_path,
            enable_subtitle=1,
            smooth_text=1,
            smooth_text_param=[1, 2, 3],
        )

        if result["success"]:
            print("✅ 音频文件转写极速版API成功！")
            if output_file:
                topspeed_client.save_result_to_file(result, output_file)
            return result
        else:
            print(f"❌ 音频文件转写极速版API失败: {result['error']}")
            if result.get("error_code") == -5000:
                print("💡 API额度不足，切换到短语音识别标准版API...")
            else:
                print("💡 尝试短语音识别标准版API作为备选方案...")

    except Exception as e:
        print(f"❌ 极速版API异常: {e}")

    # 方案2：标准版API（备选）
    print("\n🔧 尝试标准版API...")
    try:
        standard_client = BaiduStandardTranscription(api_key, secret_key)
        result = standard_client.transcribe_local_file(audio_file_path)

        if result["success"]:
            print("✅ 标准版API成功！")
            if output_file:
                standard_client.save_result_to_file(result, output_file)
            return result
        else:
            print(f"❌ 标准版API失败: {result['error']}")

    except Exception as e:
        print(f"❌ 标准版API异常: {e}")

    # 如果都失败了
    print("\n❌ 所有转写方案都失败了")
    print("💡 建议：")
    print("1. 检查API凭证是否正确")
    print("2. 检查网络连接")
    print("3. 检查API额度：https://console.bce.baidu.com/")
    print("4. 领取免费额度：https://cloud.baidu.com/doc/SPEECH/s/Wl9mh4doe")

    return {"success": False, "error": "所有转写方案都失败"}


def batch_transcription(audio_files: list, output_dir: str = "transcription_results"):
    """
    批量音频转写

    Args:
        audio_files: 音频文件路径列表
        output_dir: 输出目录
    """
    print(f"🎵 开始批量音频转写，共 {len(audio_files)} 个文件")
    print("=" * 60)

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    results = []
    successful = 0
    failed = 0

    for i, audio_file in enumerate(audio_files, 1):
        print(
            f"\n📁 处理第 {i}/{len(audio_files)} 个文件: {os.path.basename(audio_file)}"
        )

        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(audio_file))[0]
        output_file = os.path.join(output_dir, f"{base_name}_转写结果.json")

        # 执行转写
        result = smart_transcription(audio_file, output_file)
        result["source_file"] = audio_file
        result["output_file"] = output_file if result["success"] else None
        results.append(result)

        if result["success"]:
            successful += 1
            print(f"✅ 成功: {os.path.basename(audio_file)}")
        else:
            failed += 1
            print(f"❌ 失败: {os.path.basename(audio_file)}")

    # 统计结果
    print("\n" + "=" * 60)
    print("📊 批量转写完成")
    print(f"   总文件数: {len(audio_files)}")
    print(f"   成功: {successful}")
    print(f"   失败: {failed}")
    print(f"   成功率: {successful / len(audio_files) * 100:.1f}%")
    print(f"   结果保存在: {output_dir}")
    print("=" * 60)

    return results


def main():
    """主函数"""
    # 测试音频文件
    test_audio_file = "data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"

    if not os.path.exists(test_audio_file):
        print(f"❌ 测试音频文件不存在: {test_audio_file}")
        print("请确保文件路径正确")
        return

    # 单文件转写测试
    print("🧪 单文件转写测试")
    result = smart_transcription(test_audio_file, "智能转写结果.json")

    if result["success"]:
        print("\n📝 转写结果预览:")
        if result.get("text"):
            print(f"   {result['text'][:200]}...")
        elif result.get("result"):
            text = " ".join(result["result"])
            print(f"   {text[:200]}...")

    # 批量转写测试（如果有多个文件）
    audio_dir = "data/初赛数据示例/健康号"
    if os.path.exists(audio_dir):
        audio_files = []
        for file in os.listdir(audio_dir):
            if file.lower().endswith((".wav", ".mp3", ".m4a")):
                audio_files.append(os.path.join(audio_dir, file))

        if len(audio_files) > 1:
            print(f"\n🧪 批量转写测试（{len(audio_files)}个文件）")
            batch_results = batch_transcription(audio_files[:3])  # 只测试前3个文件


if __name__ == "__main__":
    main()
