#!/usr/bin/env python3
"""
百度语音识别API测试脚本
基于百度智能云语音识别标准版API文档：https://cloud.baidu.com/doc/SPEECH/s/Jlbxdezuf
"""

import base64
import json
import os
from typing import Any, Dict, List

import requests


class BaiduSpeechRecognizer:
    """百度语音识别客户端"""

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化语音识别客户端

        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.base_url = "http://vop.baidu.com/server_api"

    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token:
            return self.access_token

        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key,
        }

        try:
            response = requests.post(url, params=params)
            response.raise_for_status()
            result = response.json()

            if "access_token" in result:
                self.access_token = result["access_token"]
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {e}")

    def split_audio_file(
        self, audio_file_path: str, chunk_duration: int = 50
    ) -> List[str]:
        """
        将音频文件分割成小段

        Args:
            audio_file_path: 音频文件路径
            chunk_duration: 每段时长（秒），默认50秒

        Returns:
            分割后的音频文件路径列表
        """
        import math
        import wave

        chunk_files = []

        try:
            with wave.open(audio_file_path, "rb") as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

                total_duration = frames / float(sample_rate)
                chunk_frames = int(chunk_duration * sample_rate)

                print(
                    f"音频总时长: {total_duration:.2f}秒，将分割成 {math.ceil(total_duration / chunk_duration)} 段"
                )

                # 读取所有音频数据
                wav_file.rewind()
                audio_data = wav_file.readframes(frames)

                # 分割音频
                for i in range(0, frames, chunk_frames):
                    start_frame = i
                    end_frame = min(i + chunk_frames, frames)

                    # 计算字节位置
                    bytes_per_frame = channels * sample_width
                    start_byte = start_frame * bytes_per_frame
                    end_byte = end_frame * bytes_per_frame

                    chunk_data = audio_data[start_byte:end_byte]

                    # 生成分割文件名
                    base_name = os.path.splitext(audio_file_path)[0]
                    chunk_file = f"{base_name}_chunk_{i // chunk_frames + 1}.wav"

                    # 写入分割文件
                    with wave.open(chunk_file, "wb") as chunk_wav:
                        chunk_wav.setnchannels(channels)
                        chunk_wav.setsampwidth(sample_width)
                        chunk_wav.setframerate(sample_rate)
                        chunk_wav.writeframes(chunk_data)

                    chunk_files.append(chunk_file)
                    print(f"创建分割文件: {chunk_file}")

        except Exception as e:
            print(f"分割音频文件失败: {e}")
            return []

        return chunk_files

    def recognize_speech(
        self, audio_file_path: str, dev_pid: int = 1537
    ) -> Dict[str, Any]:
        """
        识别音频文件中的语音

        Args:
            audio_file_path: 音频文件路径（支持pcm、wav、amr、m4a格式）
            dev_pid: 语言模型ID，默认1537（普通话识别）

        Returns:
            识别结果字典
        """
        if not os.path.exists(audio_file_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_file_path}")

        # 支持的音频格式
        supported_formats = [".pcm", ".wav", ".amr", ".m4a"]
        file_ext = os.path.splitext(audio_file_path)[1].lower()
        if file_ext not in supported_formats:
            raise ValueError(
                f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}"
            )

        try:
            # 读取音频文件
            with open(audio_file_path, "rb") as f:
                audio_data = f.read()

            # 检查文件大小（标准版限制更宽松，一般支持60MB）
            file_size = len(audio_data)
            if file_size > 60 * 1024 * 1024:
                raise ValueError(f"音频文件过大: {file_size}字节，最大支持60MB")

            # 编码音频数据
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            # 获取访问令牌
            access_token = self.get_access_token()

            # 构造请求数据
            url = self.base_url

            data = {
                "format": file_ext[1:],  # 去掉点号
                "rate": 16000,  # 采样率
                "channel": 1,  # 声道数
                "cuid": "python_test",
                "token": access_token,
                "speech": audio_base64,
                "len": file_size,
                "dev_pid": dev_pid,
            }

            headers = {"Content-Type": "application/json", "Accept": "application/json"}

            # 发送请求
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()

            result = response.json()

            if result.get("err_no") == 0:
                return {
                    "success": True,
                    "text": result.get("result", [""])[0],
                    "confidence": result.get("result", ["", 0])[1]
                    if len(result.get("result", [])) > 1
                    else None,
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("err_msg", "未知错误"),
                    "error_code": result.get("err_no"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {e}", "error_code": -1}
        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -2}

    def recognize_large_audio(
        self, audio_file_path: str, use_standard_api: bool = False
    ) -> Dict[str, Any]:
        """
        识别大音频文件，自动分割后识别

        Args:
            audio_file_path: 音频文件路径
            use_standard_api: 是否使用标准版API（支持更大文件）

        Returns:
            识别结果字典
        """
        try:
            # 检查文件大小和时长
            file_size = os.path.getsize(audio_file_path)
            print(f"音频文件大小: {file_size / 1024 / 1024:.2f} MB")

            # 如果文件较小，直接识别
            if file_size < 10 * 1024 * 1024:  # 小于10MB
                print("文件较小，直接识别...")
                return self.recognize_speech(audio_file_path)

            # 文件较大，分割后识别
            print("文件较大，开始分割...")
            chunk_files = self.split_audio_file(audio_file_path, chunk_duration=50)

            if not chunk_files:
                return {"success": False, "error": "音频分割失败", "error_code": -3}

            # 逐个识别分割文件
            all_results = []
            full_text = ""

            for i, chunk_file in enumerate(chunk_files):
                print(f"\n正在识别第 {i + 1}/{len(chunk_files)} 段...")

                # 暂时都使用极速版API
                result = self.recognize_speech(chunk_file)

                if result["success"]:
                    chunk_text = result["text"]
                    full_text += chunk_text + " "
                    all_results.append(
                        {"chunk": i + 1, "text": chunk_text, "file": chunk_file}
                    )
                    print(f"第 {i + 1} 段识别结果: {chunk_text}")
                else:
                    print(f"第 {i + 1} 段识别失败: {result['error']}")
                    all_results.append(
                        {"chunk": i + 1, "error": result["error"], "file": chunk_file}
                    )

                # 清理临时文件
                try:
                    os.remove(chunk_file)
                except Exception:
                    pass

            return {
                "success": True,
                "text": full_text.strip(),
                "chunks": all_results,
                "total_chunks": len(chunk_files),
            }

        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -4}


def test_recognition():
    """测试语音识别功能"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv(
        "BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo"
    )

    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        print("示例：")
        print("export BAIDU_API_KEY='你的API密钥'")
        print("export BAIDU_SECRET_KEY='你的Secret密钥'")
        return

    # 创建识别器实例
    recognizer = BaiduSpeechRecognizer(api_key, secret_key)

    # 测试音频文件路径
    test_audio_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"

    if os.path.exists(test_audio_path):
        print(f"正在识别音频文件: {test_audio_path}")

        # 先尝试使用大文件识别功能
        result = recognizer.recognize_large_audio(
            test_audio_path, use_standard_api=False
        )

        if result["success"]:
            print("\n" + "=" * 50)
            print("识别成功!")
            print(f"识别结果: {result['text']}")
            if "chunks" in result:
                print(f"总共分割了 {result['total_chunks']} 段")
            print("=" * 50)
        else:
            print(f"\n识别失败: {result['error']} (错误码: {result['error_code']})")

            # 如果大文件识别失败，尝试直接识别
            print("\n尝试直接识别...")
            result2 = recognizer.recognize_speech(test_audio_path)
            if result2["success"]:
                print(f"直接识别成功: {result2['text']}")
            else:
                print(f"直接识别也失败: {result2['error']}")
    else:
        print(f"警告: 测试音频文件 '{test_audio_path}' 不存在")
        print("请准备一个音频文件用于测试，支持的格式: pcm, wav, amr, m4a")
        print("或使用以下命令生成测试音频:")
        print(
            'ffmpeg -f lavfi -i "sine=frequency=1000:duration=3" -ar 16000 -ac 1 test_audio.wav'
        )


if __name__ == "__main__":
    test_recognition()
