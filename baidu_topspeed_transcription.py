#!/usr/bin/env python3
"""
百度音频文件转写极速版API客户端
基于百度智能云音频文件转写极速版API：https://cloud.baidu.com/doc/SPEECH/s/Clhohwkbv
支持直接上传本地文件，无需云存储，极速返回结果
"""

import base64
import json
import os
from typing import Any, Dict, List, Optional

import requests


class BaiduTopSpeedTranscription:
    """百度音频文件转写极速版客户端"""

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化客户端

        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.api_url = (
            "https://aip.baidubce.com/rest/2.0/speech/publiccloudspeech/v1/asr/topspeed"
        )

    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token:
            return self.access_token

        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key,
        }

        try:
            response = requests.post(url, params=params)
            response.raise_for_status()
            result = response.json()

            if "access_token" in result:
                self.access_token = result["access_token"]
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {e}")

    def transcribe_local_file(
        self,
        audio_file_path: str,
        enable_subtitle: int = 1,
        subtitle_punc: int = 1,
        smooth_text: int = 1,
        smooth_text_param: Optional[List[int]] = None,
        filter_sensitive: int = 0,
    ) -> Dict[str, Any]:
        """
        转写本地音频文件

        Args:
            audio_file_path: 本地音频文件路径
            enable_subtitle: 字幕功能 0（关闭）, 1（开启）, 2（开启字幕模式，返回字粒度时间戳）
            subtitle_punc: 字幕结果中是否有标点 0（过滤标点）, 1（不过滤标点）
            smooth_text: 文本顺滑 0（不开启）, 1（开启）
            smooth_text_param: 具体开启的文本顺滑功能 [1（标点），2（数字），3（口语）]
            filter_sensitive: 敏感词过滤 0（不开启）, 1（开启）

        Returns:
            转写结果字典
        """
        print(f"开始处理音频文件: {audio_file_path}")

        # 检查文件是否存在
        if not os.path.exists(audio_file_path):
            return {
                "success": False,
                "error": f"文件不存在: {audio_file_path}",
                "error_code": -1,
            }

        # 检查文件格式
        supported_formats = [".pcm", ".wav", ".mp3", ".m4a", ".mp4", ".mov"]
        file_ext = os.path.splitext(audio_file_path)[1].lower()
        if file_ext not in supported_formats:
            return {
                "success": False,
                "error": f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}",
                "error_code": -2,
            }

        # 检查文件大小（500MB限制）
        file_size = os.path.getsize(audio_file_path)
        if file_size > 500 * 1024 * 1024:
            return {
                "success": False,
                "error": f"文件过大: {file_size / 1024 / 1024:.2f}MB，最大支持500MB",
                "error_code": -3,
            }

        print(f"文件大小: {file_size / 1024 / 1024:.2f}MB")
        print(f"文件格式: {file_ext}")

        try:
            # 读取并编码音频文件
            print("正在读取和编码音频文件...")
            with open(audio_file_path, "rb") as f:
                audio_data = f.read()

            audio_base64 = base64.b64encode(audio_data).decode("utf-8")
            print(f"文件编码完成，base64长度: {len(audio_base64)}")

            # 获取访问令牌
            access_token = self.get_access_token()

            # 构造请求数据
            data = {
                "access_token": access_token,
                "rate": 16000,  # 固定值
                "cuid": "python_topspeed_client",
                "dev_pid": 80006,  # 固定值，音视频字幕模型
                "speech": audio_base64,
                "enable_subtitle": enable_subtitle,
                "subtitle_punc": subtitle_punc,
                "smooth_text": smooth_text,
                "filter_sensitive": filter_sensitive,
            }

            # 添加文本顺滑参数
            if smooth_text == 1 and smooth_text_param:
                data["smooth_text_param"] = smooth_text_param

            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }

            print("正在发送转写请求...")
            # 发送请求
            response = requests.post(
                self.api_url, data=json.dumps(data), headers=headers, timeout=300
            )
            response.raise_for_status()

            result = response.json()
            print("收到转写响应")

            if result.get("error_code") == 0:
                return {
                    "success": True,
                    "result": result.get("result", []),
                    "audio_duration": result.get("audio_duration"),
                    "detailed_result": result.get("detailed_result", []),
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error_message", "未知错误"),
                    "error_code": result.get("error_code"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"网络请求失败: {e}",
                "error_code": -4,
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_code": -5,
            }

    def transcribe_with_url(
        self,
        speech_url: str,
        enable_subtitle: int = 1,
        subtitle_punc: int = 1,
        smooth_text: int = 1,
        smooth_text_param: Optional[List[int]] = None,
        filter_sensitive: int = 0,
    ) -> Dict[str, Any]:
        """
        使用URL转写音频文件

        Args:
            speech_url: 音频文件URL（需要公网可访问）
            其他参数同transcribe_local_file

        Returns:
            转写结果字典
        """
        print(f"开始处理音频URL: {speech_url}")

        try:
            # 获取访问令牌
            access_token = self.get_access_token()

            # 构造请求数据
            data = {
                "access_token": access_token,
                "rate": 16000,  # 固定值
                "cuid": "python_topspeed_client",
                "dev_pid": 80006,  # 固定值，音视频字幕模型
                "speech_url": speech_url,
                "enable_subtitle": enable_subtitle,
                "subtitle_punc": subtitle_punc,
                "smooth_text": smooth_text,
                "filter_sensitive": filter_sensitive,
            }

            # 添加文本顺滑参数
            if smooth_text == 1 and smooth_text_param:
                data["smooth_text_param"] = smooth_text_param

            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }

            print("正在发送转写请求...")
            # 发送请求
            response = requests.post(
                self.api_url, data=json.dumps(data), headers=headers, timeout=300
            )
            response.raise_for_status()

            result = response.json()
            print("收到转写响应")

            if result.get("error_code") == 0:
                return {
                    "success": True,
                    "result": result.get("result", []),
                    "audio_duration": result.get("audio_duration"),
                    "detailed_result": result.get("detailed_result", []),
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error_message", "未知错误"),
                    "error_code": result.get("error_code"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"网络请求失败: {e}",
                "error_code": -4,
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_code": -5,
            }

    def save_result_to_file(self, result: Dict[str, Any], output_file: str):
        """
        保存转写结果到本地文件

        Args:
            result: 转写结果
            output_file: 输出文件路径
        """
        if not result.get("success"):
            print(f"转写失败，无法保存结果: {result.get('error')}")
            return

        # 准备保存的数据
        save_data = {"完整转写结果": "", "音频时长": "未知", "分段详细结果": []}

        # 处理极速版API的结果格式
        if result.get("result") and isinstance(result["result"], list):
            save_data["完整转写结果"] = " ".join(result["result"])

        # 处理标准版API的结果格式（有text字段）
        elif result.get("text"):
            save_data["完整转写结果"] = result["text"]

        # 处理音频时长
        if result.get("audio_duration"):
            if isinstance(result["audio_duration"], (int, float)):
                if result["audio_duration"] > 1000:  # 毫秒格式
                    save_data["音频时长"] = f"{result['audio_duration'] / 1000:.2f} 秒"
                else:  # 秒格式
                    save_data["音频时长"] = f"{result['audio_duration']:.2f} 秒"

        # 处理极速版API的详细分段结果
        if result.get("detailed_result"):
            for i, segment in enumerate(result["detailed_result"]):
                start_time = segment.get("begin_time", 0) / 1000
                end_time = segment.get("end_time", 0) / 1000
                text = " ".join(segment.get("res", []))

                save_data["分段详细结果"].append(
                    {
                        "序号": i + 1,
                        "开始时间": f"{start_time:.2f}s",
                        "结束时间": f"{end_time:.2f}s",
                        "文本": text,
                        "分句ID": segment.get("sn"),
                        "整段ID": segment.get("corpus_no"),
                    }
                )

        # 处理标准版API的分段结果
        elif result.get("chunks"):
            for chunk in result["chunks"]:
                if chunk.get("text"):  # 只保存成功识别的分段
                    save_data["分段详细结果"].append(
                        {
                            "序号": chunk.get("chunk"),
                            "开始时间": f"{chunk.get('start_time', 0):.1f}s",
                            "结束时间": f"{chunk.get('end_time', 0):.1f}s",
                            "文本": chunk.get("text"),
                            "置信度": chunk.get("confidence"),
                        }
                    )

        # 保存到文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"转写结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")


def test_your_audio_file():
    """测试你的音频文件"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv(
        "BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo"
    )

    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return

    # 创建转写客户端
    client = BaiduTopSpeedTranscription(api_key, secret_key)

    # 你的音频文件路径
    audio_file_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"

    print("=" * 60)
    print("🚀 百度音频文件转写极速版API测试")
    print("=" * 60)

    if not os.path.exists(audio_file_path):
        print(f"❌ 错误：音频文件不存在: {audio_file_path}")
        return

    # 执行转写
    print("正在尝试极速版API...")
    result = client.transcribe_local_file(
        audio_file_path=audio_file_path,
        enable_subtitle=1,  # 开启字幕功能
        subtitle_punc=1,  # 保留标点
        smooth_text=1,  # 开启文本顺滑
        smooth_text_param=[1, 2, 3],  # 开启标点+数字+口语优化
        filter_sensitive=0,  # 不过滤敏感词
    )

    # 如果极速版失败，提供解决方案
    if not result["success"] and result.get("error_code") == -5000:
        print("\n⚠️  极速版API达到使用限制")
        print("💡 解决方案：")
        print("1. 登录百度智能云控制台检查额度：https://console.bce.baidu.com/")
        print("2. 领取免费测试资源：https://cloud.baidu.com/doc/SPEECH/s/Wl9mh4doe")
        print("3. 购买语音识别服务包")
        print("\n🔄 正在尝试使用标准版API作为备选方案...")

        # 导入并使用标准版API
        try:
            from local_audio_transcription import LocalAudioTranscription

            backup_client = LocalAudioTranscription(api_key, secret_key)
            result = backup_client.transcribe_local_file(audio_file_path)
            print("✅ 标准版API调用成功！")
        except ImportError:
            print("❌ 标准版API备选方案不可用")
            return
        except Exception as e:
            print(f"❌ 标准版API也失败了: {e}")
            return

    if result["success"]:
        print("\n" + "=" * 50)
        print("🎉 转写成功!")
        print("=" * 50)

        # 显示完整转写结果
        full_text = " ".join(result.get("result", []))
        print("📝 完整转写结果:")
        print(f"   {full_text}")

        # 显示统计信息
        if result.get("audio_duration"):
            print("\n📊 统计信息:")
            print(f"   音频时长: {result['audio_duration'] / 1000:.2f} 秒")

        # 显示详细分段结果
        if result.get("detailed_result"):
            print("\n📋 分段详细结果:")
            for i, segment in enumerate(result["detailed_result"]):
                start_time = segment.get("begin_time", 0) / 1000
                end_time = segment.get("end_time", 0) / 1000
                text = " ".join(segment.get("res", []))
                print(f"   [{start_time:6.2f}s - {end_time:6.2f}s]: {text}")

        # 保存结果到本地文件
        output_file = "极速版转写结果.json"
        client.save_result_to_file(result, output_file)

        print("\n" + "=" * 50)
        print("✅ 转写完成！结果已保存到本地文件")
        print("=" * 50)

    else:
        print(f"\n❌ 转写失败: {result['error']}")
        if result.get("error_code"):
            print(f"   错误码: {result['error_code']}")

        # 显示原始响应以便调试
        if result.get("raw_result"):
            print(f"   原始响应: {result['raw_result']}")


if __name__ == "__main__":
    test_your_audio_file()
