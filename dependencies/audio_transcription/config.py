#!/usr/bin/env python3
"""
百度音频转写配置文件
"""

import os
from typing import Any, Dict


class TranscriptionConfig:
    """转写配置类"""

    # API配置
    DEFAULT_API_KEY = "tglobxgL5s1lfpv5wGmdTlZD"
    DEFAULT_SECRET_KEY = "7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo"

    # API端点
    ENDPOINTS = {
        "token": "https://aip.baidubce.com/oauth/2.0/token",
        "topspeed": "https://aip.baidubce.com/rest/2.0/speech/publiccloudspeech/v1/asr/topspeed",
        "standard": "http://vop.baidu.com/server_api",
        "file_create": "https://aip.baidubce.com/rpc/2.0/aasr/v1/create",
        "file_query": "https://aip.baidubce.com/rpc/2.0/aasr/v1/query",
    }

    # 音频文件转写极速版API配置
    TOPSPEED_CONFIG = {
        "rate": 16000,  # 固定值
        "dev_pid": 80006,  # 音视频字幕模型
        "enable_subtitle": 1,  # 开启字幕功能
        "subtitle_punc": 1,  # 保留标点
        "smooth_text": 1,  # 开启文本顺滑
        "smooth_text_param": [1, 2, 3],  # 标点+数字+口语优化
        "filter_sensitive": 0,  # 不过滤敏感词
        "max_file_size": 500 * 1024 * 1024,  # 500MB
        "max_duration": 3600,  # 1小时
    }

    # 短语音识别标准版API配置
    STANDARD_CONFIG = {
        "rate": 16000,
        "channel": 1,
        "dev_pid": 1537,  # 普通话识别
        "max_file_size": 60 * 1024 * 1024,  # 60MB
        "chunk_duration": 50,  # 分割时长（秒）
    }

    # 音频文件转写标准版API配置
    FILE_CONFIG = {
        "rate": 16000,
        "pid": 80001,  # 中文极速版
        "smooth_text": 1,
        "filter_sensitive": 0,
        "max_file_size": 500 * 1024 * 1024,  # 500MB
        "max_duration": 3600,  # 1小时
        "max_wait_time": 3600,  # 最大等待时间
        "check_interval": 30,  # 检查间隔
    }

    # 支持的音频格式
    SUPPORTED_FORMATS = {
        "topspeed": [".pcm", ".wav", ".mp3", ".m4a", ".mp4", ".mov"],
        "standard": [".pcm", ".wav", ".amr", ".m4a"],
        "file": [".pcm", ".wav", ".mp3", ".m4a", ".amr"],
    }

    @classmethod
    def get_api_credentials(cls) -> tuple:
        """获取API凭证"""
        api_key = os.getenv("BAIDU_API_KEY", cls.DEFAULT_API_KEY)
        secret_key = os.getenv("BAIDU_SECRET_KEY", cls.DEFAULT_SECRET_KEY)
        return api_key, secret_key

    @classmethod
    def get_config(cls, api_type: str) -> Dict[str, Any]:
        """获取指定API类型的配置"""
        configs = {
            "topspeed": cls.TOPSPEED_CONFIG,
            "standard": cls.STANDARD_CONFIG,
            "file": cls.FILE_CONFIG,
        }
        return configs.get(api_type, {})

    @classmethod
    def get_supported_formats(cls, api_type: str) -> list:
        """获取支持的音频格式"""
        return cls.SUPPORTED_FORMATS.get(api_type, [])

    @classmethod
    def validate_file(cls, file_path: str, api_type: str) -> tuple:
        """
        验证文件是否符合要求

        Returns:
            (is_valid: bool, error_message: str)
        """
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"

        # 检查文件格式
        file_ext = os.path.splitext(file_path)[1].lower()
        supported_formats = cls.get_supported_formats(api_type)
        if file_ext not in supported_formats:
            return (
                False,
                f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}",
            )

        # 检查文件大小
        file_size = os.path.getsize(file_path)
        config = cls.get_config(api_type)
        max_size = config.get("max_file_size", 0)

        if max_size > 0 and file_size > max_size:
            return (
                False,
                f"文件过大: {file_size / 1024 / 1024:.2f}MB，最大支持: {max_size / 1024 / 1024:.0f}MB",
            )

        return True, ""


# 错误码映射
ERROR_CODES = {
    -5000: "API额度不足或达到使用限制",
    3300: "token参数错误",
    3301: "音频质量过差",
    3302: "鉴权失败",
    3303: "语音服务器后端问题",
    3304: "用户的请求QPS超限",
    3305: "用户的日pv（日请求量）超限",
    3307: "语音服务器后端识别出错问题",
    3308: "音频过长",
    3309: "音频数据问题",
    3310: "音频数据过长",
    3311: "音频采样率问题",
    3312: "音频声道数问题",
}


def get_error_message(error_code: int) -> str:
    """获取错误码对应的中文说明"""
    return ERROR_CODES.get(error_code, f"未知错误码: {error_code}")


# 使用示例
if __name__ == "__main__":
    config = TranscriptionConfig()

    print("API凭证:", config.get_api_credentials())
    print("极速版配置:", config.get_config("topspeed"))
    print("支持格式:", config.get_supported_formats("topspeed"))

    # 测试文件验证
    test_file = "/path/to/test.wav"
    is_valid, error = config.validate_file(test_file, "topspeed")
    print(f"文件验证: {is_valid}, {error}")
