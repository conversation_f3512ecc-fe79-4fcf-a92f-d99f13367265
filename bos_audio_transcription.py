#!/usr/bin/env python3
"""
使用百度云对象存储BOS的音频转写解决方案
完整支持本地文件上传和转写
"""

import os
import time
import uuid
from typing import Any, Dict, List, Optional

from baidu_audio_transcription import BaiduAudioTranscription

# 如果需要使用BOS，需要安装：pip install bce-python-sdk
try:
    from baidubce.bce_client_configuration import BceClientConfiguration
    from baidubce.auth.bce_credentials import BceCredentials
    from baidubce.services.bos.bos_client import BosClient
    BOS_AVAILABLE = True
except ImportError:
    BOS_AVAILABLE = False


class BOSAudioTranscription:
    """使用百度云对象存储BOS的音频转写解决方案"""
    
    def __init__(
        self, 
        api_key: str, 
        secret_key: str,
        bos_access_key: str = None,
        bos_secret_key: str = None,
        bos_endpoint: str = "bj.bcebos.com",
        bucket_name: str = "audio-transcription"
    ):
        """
        初始化
        
        Args:
            api_key: 百度智能云API Key（用于语音识别）
            secret_key: 百度智能云Secret Key（用于语音识别）
            bos_access_key: BOS Access Key（用于对象存储）
            bos_secret_key: BOS Secret Key（用于对象存储）
            bos_endpoint: BOS端点
            bucket_name: BOS存储桶名称
        """
        self.transcription_client = BaiduAudioTranscription(api_key, secret_key)
        
        # BOS配置
        self.bos_access_key = bos_access_key
        self.bos_secret_key = bos_secret_key
        self.bos_endpoint = bos_endpoint
        self.bucket_name = bucket_name
        self.bos_client = None
        
        if BOS_AVAILABLE and bos_access_key and bos_secret_key:
            self._init_bos_client()
    
    def _init_bos_client(self):
        """初始化BOS客户端"""
        try:
            config = BceClientConfiguration(
                credentials=BceCredentials(self.bos_access_key, self.bos_secret_key),
                endpoint=self.bos_endpoint
            )
            self.bos_client = BosClient(config)
            print("BOS客户端初始化成功")
        except Exception as e:
            print(f"BOS客户端初始化失败: {e}")
            self.bos_client = None
    
    def upload_to_bos(self, local_file_path: str) -> Optional[str]:
        """
        上传文件到百度云对象存储BOS
        
        Args:
            local_file_path: 本地文件路径
            
        Returns:
            上传后的公网URL，失败返回None
        """
        if not BOS_AVAILABLE:
            print("错误：未安装bce-python-sdk，请运行：pip install bce-python-sdk")
            return None
        
        if not self.bos_client:
            print("错误：BOS客户端未初始化，请检查BOS配置")
            return None
        
        if not os.path.exists(local_file_path):
            print(f"文件不存在: {local_file_path}")
            return None
        
        # 检查文件大小（500MB限制）
        file_size = os.path.getsize(local_file_path)
        if file_size > 500 * 1024 * 1024:
            print(f"文件过大: {file_size/1024/1024:.2f}MB，最大支持500MB")
            return None
        
        try:
            # 生成唯一的对象键
            file_ext = os.path.splitext(local_file_path)[1]
            object_key = f"audio/{uuid.uuid4().hex}{file_ext}"
            
            print(f"正在上传文件到BOS: {object_key}")
            print(f"文件大小: {file_size/1024/1024:.2f}MB")
            
            # 上传文件
            with open(local_file_path, 'rb') as f:
                self.bos_client.put_object(
                    bucket_name=self.bucket_name,
                    key=object_key,
                    data=f,
                    content_length=file_size
                )
            
            # 构造公网访问URL
            public_url = f"https://{self.bucket_name}.{self.bos_endpoint}/{object_key}"
            print(f"文件上传成功: {public_url}")
            
            return public_url
            
        except Exception as e:
            print(f"文件上传失败: {e}")
            return None
    
    def delete_from_bos(self, object_key: str) -> bool:
        """
        从BOS删除文件
        
        Args:
            object_key: 对象键
            
        Returns:
            删除是否成功
        """
        if not self.bos_client:
            return False
        
        try:
            self.bos_client.delete_object(self.bucket_name, object_key)
            print(f"文件删除成功: {object_key}")
            return True
        except Exception as e:
            print(f"文件删除失败: {e}")
            return False
    
    def transcribe_local_file(
        self, 
        local_file_path: str,
        format: str = None,
        pid: int = 80001,
        smooth_text: int = 1,
        filter_sensitive: int = 0,
        wait_for_result: bool = True,
        cleanup_after: bool = True
    ) -> Dict[str, Any]:
        """
        转写本地音频文件
        
        Args:
            local_file_path: 本地音频文件路径
            format: 音频格式，如果不指定则从文件扩展名推断
            pid: 语言类型
            smooth_text: 文本顺滑
            filter_sensitive: 敏感词过滤
            wait_for_result: 是否等待结果
            cleanup_after: 转写完成后是否删除云端文件
            
        Returns:
            转写结果字典
        """
        print(f"开始处理本地音频文件: {local_file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            return {
                "success": False,
                "error": f"文件不存在: {local_file_path}",
                "error_code": -1
            }
        
        # 推断音频格式
        if format is None:
            file_ext = os.path.splitext(local_file_path)[1].lower()
            format_map = {
                '.wav': 'wav',
                '.mp3': 'mp3',
                '.pcm': 'pcm',
                '.m4a': 'm4a',
                '.amr': 'amr'
            }
            format = format_map.get(file_ext)
            if format is None:
                return {
                    "success": False,
                    "error": f"不支持的音频格式: {file_ext}",
                    "error_code": -2
                }
        
        print(f"音频格式: {format}")
        
        # 上传文件到BOS
        speech_url = self.upload_to_bos(local_file_path)
        
        if speech_url is None:
            return {
                "success": False,
                "error": "文件上传失败",
                "error_code": -3
            }
        
        # 执行转写
        result = self.transcription_client.transcribe_audio_url(
            speech_url=speech_url,
            format=format,
            pid=pid,
            smooth_text=smooth_text,
            filter_sensitive=filter_sensitive,
            wait_for_result=wait_for_result
        )
        
        # 清理云端文件
        if cleanup_after and speech_url:
            try:
                # 从URL提取object_key
                object_key = speech_url.split(f"{self.bucket_name}.{self.bos_endpoint}/")[1]
                self.delete_from_bos(object_key)
            except Exception as e:
                print(f"清理云端文件失败: {e}")
        
        return result


def setup_bos_guide():
    """显示BOS配置指南"""
    print("\n" + "="*60)
    print("百度云对象存储BOS配置指南")
    print("="*60)
    print("1. 注册百度智能云账号：https://cloud.baidu.com/")
    print("2. 开通对象存储BOS服务：https://cloud.baidu.com/product/bos.html")
    print("3. 创建存储桶（Bucket）")
    print("4. 获取Access Key和Secret Key")
    print("5. 安装BOS SDK：pip install bce-python-sdk")
    print()
    print("环境变量设置：")
    print("export BOS_ACCESS_KEY='你的BOS_Access_Key'")
    print("export BOS_SECRET_KEY='你的BOS_Secret_Key'")
    print("export BOS_BUCKET_NAME='你的存储桶名称'")
    print()
    print("注意事项：")
    print("- 存储桶需要设置为公共读权限")
    print("- 建议设置生命周期规则自动删除临时文件")
    print("- 注意存储和流量费用")
    print("="*60)


def test_bos_transcription():
    """测试BOS音频转写"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv("BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo")
    
    # BOS配置
    bos_access_key = os.getenv("BOS_ACCESS_KEY")
    bos_secret_key = os.getenv("BOS_SECRET_KEY")
    bucket_name = os.getenv("BOS_BUCKET_NAME", "audio-transcription")
    
    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return
    
    if not bos_access_key or not bos_secret_key:
        print("警告：未设置BOS配置，将使用模拟上传")
        setup_bos_guide()
        return
    
    # 创建BOS转写客户端
    client = BOSAudioTranscription(
        api_key=api_key,
        secret_key=secret_key,
        bos_access_key=bos_access_key,
        bos_secret_key=bos_secret_key,
        bucket_name=bucket_name
    )
    
    # 测试音频文件路径
    test_audio_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"
    
    print("开始BOS音频转写测试...")
    
    if os.path.exists(test_audio_path):
        # 执行转写
        result = client.transcribe_local_file(
            local_file_path=test_audio_path,
            pid=80001,  # 中文极速版
            smooth_text=1,  # 开启文本顺滑
            wait_for_result=True,
            cleanup_after=True  # 转写完成后删除云端文件
        )
        
        if result["success"]:
            print("\n" + "="*50)
            print("转写成功!")
            print(f"转写结果: {result['result']}")
            if result.get('audio_duration'):
                print(f"音频时长: {result['audio_duration']/1000:.2f} 秒")
            
            # 显示详细分段结果
            if result.get('detailed_result'):
                print("\n分段详细结果:")
                for i, segment in enumerate(result['detailed_result']):
                    start_time = segment.get('begin_time', 0) / 1000
                    end_time = segment.get('end_time', 0) / 1000
                    text = ' '.join(segment.get('res', []))
                    print(f"  [{start_time:.2f}s - {end_time:.2f}s]: {text}")
            
            print("="*50)
        else:
            print(f"\n转写失败: {result['error']} (错误码: {result.get('error_code')})")
    else:
        print(f"测试音频文件不存在: {test_audio_path}")


if __name__ == "__main__":
    test_bos_transcription()
