#!/usr/bin/env python3
"""
百度短语音识别标准版API客户端
基于百度智能云短语音识别标准版API：https://cloud.baidu.com/doc/SPEECH/s/Jlbxdezuf
使用短语音识别API + 自动分割，无需上传到云存储
特点：自动分割大文件，稳定可靠，适合作为备选方案
"""

import base64
import json
import math
import os
import wave
from typing import Any, Dict, List

import requests


class BaiduShortSpeechStandardTranscription:
    """百度短语音识别标准版API客户端"""

    def __init__(self, api_key: str, secret_key: str):
        """
        初始化客户端

        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.base_url = "http://vop.baidu.com/server_api"

    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token:
            return self.access_token

        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key,
        }

        try:
            response = requests.post(url, params=params)
            response.raise_for_status()
            result = response.json()

            if "access_token" in result:
                self.access_token = result["access_token"]
                return self.access_token
            else:
                raise Exception(f"获取access_token失败: {result}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {e}")

    def split_audio_file(
        self, audio_file_path: str, chunk_duration: int = 50
    ) -> List[str]:
        """
        将音频文件分割成小段

        Args:
            audio_file_path: 音频文件路径
            chunk_duration: 每段时长（秒），默认50秒

        Returns:
            分割后的音频文件路径列表
        """
        chunk_files = []

        try:
            with wave.open(audio_file_path, "rb") as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

                total_duration = frames / float(sample_rate)
                chunk_frames = int(chunk_duration * sample_rate)

                print(f"音频总时长: {total_duration:.2f}秒")
                print(f"将分割成 {math.ceil(total_duration / chunk_duration)} 段")

                # 读取所有音频数据
                wav_file.rewind()
                audio_data = wav_file.readframes(frames)

                # 分割音频
                for i in range(0, frames, chunk_frames):
                    start_frame = i
                    end_frame = min(i + chunk_frames, frames)

                    # 计算字节位置
                    bytes_per_frame = channels * sample_width
                    start_byte = start_frame * bytes_per_frame
                    end_byte = end_frame * bytes_per_frame

                    chunk_data = audio_data[start_byte:end_byte]

                    # 生成分割文件名
                    base_name = os.path.splitext(audio_file_path)[0]
                    chunk_file = f"{base_name}_chunk_{i // chunk_frames + 1}.wav"

                    # 写入分割文件
                    with wave.open(chunk_file, "wb") as chunk_wav:
                        chunk_wav.setnchannels(channels)
                        chunk_wav.setsampwidth(sample_width)
                        chunk_wav.setframerate(sample_rate)
                        chunk_wav.writeframes(chunk_data)

                    chunk_files.append(chunk_file)

                    # 计算时间范围
                    start_time = start_frame / sample_rate
                    end_time = end_frame / sample_rate
                    print(
                        f"创建分割文件: {os.path.basename(chunk_file)} [{start_time:.1f}s - {end_time:.1f}s]"
                    )

        except Exception as e:
            print(f"分割音频文件失败: {e}")
            return []

        return chunk_files

    def recognize_speech(
        self, audio_file_path: str, dev_pid: int = 1537
    ) -> Dict[str, Any]:
        """
        识别单个音频文件

        Args:
            audio_file_path: 音频文件路径
            dev_pid: 语言模型ID，默认1537（普通话识别）

        Returns:
            识别结果字典
        """
        if not os.path.exists(audio_file_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_file_path}")

        # 支持的音频格式
        supported_formats = [".pcm", ".wav", ".amr", ".m4a"]
        file_ext = os.path.splitext(audio_file_path)[1].lower()
        if file_ext not in supported_formats:
            raise ValueError(
                f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}"
            )

        try:
            # 读取音频文件
            with open(audio_file_path, "rb") as f:
                audio_data = f.read()

            # 检查文件大小（标准版限制更宽松，一般支持60MB）
            file_size = len(audio_data)
            if file_size > 60 * 1024 * 1024:
                raise ValueError(f"音频文件过大: {file_size}字节，最大支持60MB")

            # 编码音频数据
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")

            # 获取访问令牌
            access_token = self.get_access_token()

            # 构造请求数据
            url = self.base_url

            data = {
                "format": file_ext[1:],  # 去掉点号
                "rate": 16000,  # 采样率
                "channel": 1,  # 声道数
                "cuid": "python_test",
                "token": access_token,
                "speech": audio_base64,
                "len": file_size,
                "dev_pid": dev_pid,
            }

            headers = {"Content-Type": "application/json", "Accept": "application/json"}

            # 发送请求
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()

            result = response.json()

            if result.get("err_no") == 0:
                return {
                    "success": True,
                    "text": result.get("result", [""])[0],
                    "confidence": result.get("result", ["", 0])[1]
                    if len(result.get("result", [])) > 1
                    else None,
                    "raw_result": result,
                }
            else:
                return {
                    "success": False,
                    "error": result.get("err_msg", "未知错误"),
                    "error_code": result.get("err_no"),
                    "raw_result": result,
                }

        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {e}", "error_code": -1}
        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -2}

    def transcribe_local_file(self, audio_file_path: str) -> Dict[str, Any]:
        """
        转写本地音频文件（自动分割处理）

        Args:
            audio_file_path: 本地音频文件路径

        Returns:
            转写结果字典
        """
        print(f"开始处理音频文件: {audio_file_path}")

        if not os.path.exists(audio_file_path):
            return {
                "success": False,
                "error": f"文件不存在: {audio_file_path}",
                "error_code": -1,
            }

        try:
            # 检查文件大小和时长
            file_size = os.path.getsize(audio_file_path)
            print(f"文件大小: {file_size / 1024 / 1024:.2f} MB")

            # 获取音频时长
            with wave.open(audio_file_path, "rb") as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                duration = frames / float(sample_rate)

            print(f"音频时长: {duration:.2f} 秒")

            # 如果文件较小且时长较短，直接识别
            if file_size < 1 * 1024 * 1024 and duration < 60:  # 小于1MB且小于60秒
                print("文件较小，直接识别...")
                return self.recognize_speech(audio_file_path)

            # 文件较大，分割后识别
            print("文件较大，开始分割...")
            chunk_files = self.split_audio_file(audio_file_path, chunk_duration=50)

            if not chunk_files:
                return {"success": False, "error": "音频分割失败", "error_code": -3}

            # 逐个识别分割文件
            all_results = []
            full_text = ""
            successful_chunks = 0

            for i, chunk_file in enumerate(chunk_files):
                print(f"\n正在识别第 {i + 1}/{len(chunk_files)} 段...")

                result = self.recognize_speech(chunk_file)

                if result["success"]:
                    chunk_text = result["text"]
                    full_text += chunk_text + " "
                    successful_chunks += 1

                    # 计算时间范围
                    start_time = i * 50
                    end_time = min((i + 1) * 50, duration)

                    all_results.append(
                        {
                            "chunk": i + 1,
                            "start_time": start_time,
                            "end_time": end_time,
                            "text": chunk_text,
                            "confidence": result.get("confidence"),
                            "file": chunk_file,
                        }
                    )
                    print(f"✅ 第 {i + 1} 段识别成功: {chunk_text}")
                else:
                    print(f"❌ 第 {i + 1} 段识别失败: {result['error']}")
                    all_results.append(
                        {
                            "chunk": i + 1,
                            "start_time": i * 50,
                            "end_time": min((i + 1) * 50, duration),
                            "error": result["error"],
                            "file": chunk_file,
                        }
                    )

                # 清理临时文件
                try:
                    os.remove(chunk_file)
                except Exception:
                    pass

            return {
                "success": True,
                "text": full_text.strip(),
                "chunks": all_results,
                "total_chunks": len(chunk_files),
                "successful_chunks": successful_chunks,
                "audio_duration": duration,
            }

        except Exception as e:
            return {"success": False, "error": str(e), "error_code": -4}

    def save_result_to_file(self, result: Dict[str, Any], output_file: str):
        """
        保存转写结果到本地文件

        Args:
            result: 转写结果
            output_file: 输出文件路径
        """
        if not result.get("success"):
            print(f"转写失败，无法保存结果: {result.get('error')}")
            return

        # 准备保存的数据
        save_data = {
            "完整转写结果": result.get("text", ""),
            "音频时长": f"{result.get('audio_duration', 0):.2f} 秒",
            "总分段数": result.get("total_chunks", 0),
            "成功分段数": result.get("successful_chunks", 0),
            "分段详细结果": [],
        }

        # 处理分段结果
        if result.get("chunks"):
            for chunk in result["chunks"]:
                chunk_data = {
                    "分段": chunk.get("chunk"),
                    "时间范围": f"{chunk.get('start_time', 0):.1f}s - {chunk.get('end_time', 0):.1f}s",
                    "文本": chunk.get("text", "识别失败"),
                }
                if chunk.get("confidence"):
                    chunk_data["置信度"] = chunk["confidence"]
                if chunk.get("error"):
                    chunk_data["错误"] = chunk["error"]

                save_data["分段详细结果"].append(chunk_data)

        # 保存到文件
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"转写结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")


def test_your_audio_file():
    """测试你的音频文件"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv(
        "BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo"
    )

    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return

    # 创建转写客户端
    client = BaiduShortSpeechStandardTranscription(api_key, secret_key)

    # 你的音频文件路径
    audio_file_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"

    print("=" * 60)
    print("🎵 开始转写你的音频文件")
    print("=" * 60)

    if not os.path.exists(audio_file_path):
        print(f"❌ 错误：音频文件不存在: {audio_file_path}")
        return

    # 执行转写
    result = client.transcribe_local_file(audio_file_path)

    if result["success"]:
        print("\n" + "=" * 50)
        print("🎉 转写成功!")
        print("=" * 50)

        # 显示转写结果
        print("📝 完整转写结果:")
        print(f"   {result['text']}")

        print("\n📊 统计信息:")
        print(f"   音频时长: {result.get('audio_duration', 0):.2f} 秒")
        print(f"   总分段数: {result.get('total_chunks', 0)}")
        print(f"   成功分段数: {result.get('successful_chunks', 0)}")

        # 显示分段详细结果
        if result.get("chunks"):
            print("\n📋 分段详细结果:")
            for chunk in result["chunks"]:
                if chunk.get("text"):
                    print(
                        f"   [{chunk['start_time']:5.1f}s - {chunk['end_time']:5.1f}s]: {chunk['text']}"
                    )
                else:
                    print(
                        f"   [{chunk['start_time']:5.1f}s - {chunk['end_time']:5.1f}s]: ❌ {chunk.get('error', '识别失败')}"
                    )

        # 保存结果到本地文件
        output_file = "音频转写结果.json"
        client.save_result_to_file(result, output_file)

        print("\n" + "=" * 50)
        print("✅ 转写完成！结果已保存到本地文件")
        print("=" * 50)

    else:
        print(f"\n❌ 转写失败: {result['error']}")
        if result.get("error_code"):
            print(f"   错误码: {result['error_code']}")


if __name__ == "__main__":
    test_your_audio_file()
