#!/usr/bin/env python3
"""
文心大模型工具包使用示例
使用整理后的dependencies目录中的工具
"""

import os
import sys

# 添加dependencies目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dependencies'))

try:
    from wenxin_llm import WenxinLLMClient, WenxinConfig
    WENXIN_AVAILABLE = True
except ImportError as e:
    WENXIN_AVAILABLE = False
    IMPORT_ERROR = str(e)


def test_basic_functionality():
    """测试基础功能"""
    print("🧪 基础功能测试")
    print("-" * 30)
    
    if not WENXIN_AVAILABLE:
        print(f"❌ 导入失败: {IMPORT_ERROR}")
        print("💡 请安装依赖: pip install langchain")
        return False
    
    try:
        # 测试配置
        print("📋 配置测试:")
        api_key, base_url = WenxinConfig.get_api_credentials()
        print(f"   API Key: {api_key[:20]}...")
        print(f"   Base URL: {base_url}")
        
        # 测试可用模型
        models = WenxinConfig.list_available_models()
        print(f"   可用模型: {list(models.keys())}")
        
        # 测试客户端初始化
        print("\n🤖 客户端测试:")
        client = WenxinLLMClient()
        print("   ✅ 客户端初始化成功")
        
        # 测试模型信息
        model_info = client.get_model_info()
        print(f"   当前模型: {model_info.get('name', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False


def test_simple_chat():
    """测试简单对话"""
    print("\n💬 简单对话测试")
    print("-" * 30)
    
    if not WENXIN_AVAILABLE:
        print("❌ 工具包不可用")
        return False
    
    try:
        client = WenxinLLMClient()
        
        # 简单问候
        print("测试问题: 你好")
        response = client.chat("你好")
        print(f"回复: {response[:100]}..." if len(response) > 100 else f"回复: {response}")
        
        # 简单问题
        print("\n测试问题: 1+1等于几？")
        response = client.chat("1+1等于几？")
        print(f"回复: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单对话测试失败: {e}")
        return False


def test_batch_processing():
    """测试批量处理"""
    print("\n📦 批量处理测试")
    print("-" * 30)
    
    if not WENXIN_AVAILABLE:
        print("❌ 工具包不可用")
        return False
    
    try:
        client = WenxinLLMClient()
        
        questions = [
            "什么是Python？",
            "解释一下人工智能",
            "今天是星期几？"
        ]
        
        print(f"批量处理 {len(questions)} 个问题...")
        responses = client.batch_chat(questions)
        
        for i, (q, a) in enumerate(zip(questions, responses), 1):
            print(f"\n问题{i}: {q}")
            print(f"回答{i}: {a[:80]}..." if len(a) > 80 else f"回答{i}: {a}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False


def test_model_switching():
    """测试模型切换"""
    print("\n🔄 模型切换测试")
    print("-" * 30)
    
    if not WENXIN_AVAILABLE:
        print("❌ 工具包不可用")
        return False
    
    try:
        client = WenxinLLMClient()
        
        # 获取可用模型
        available_models = list(WenxinConfig.list_available_models().keys())
        print(f"可用模型: {available_models}")
        
        test_question = "请用一句话介绍你自己"
        
        # 测试前两个模型
        for model in available_models[:2]:
            try:
                print(f"\n🤖 测试模型: {model}")
                client.switch_model(model)
                response = client.chat(test_question)
                print(f"   回复: {response[:100]}..." if len(response) > 100 else f"   回复: {response}")
            except Exception as e:
                print(f"   ❌ 模型 {model} 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型切换测试失败: {e}")
        return False


def interactive_chat():
    """交互式聊天"""
    print("\n💬 交互式聊天")
    print("-" * 30)
    print("输入 'quit' 或 'exit' 退出")
    
    if not WENXIN_AVAILABLE:
        print("❌ 工具包不可用")
        return
    
    try:
        client = WenxinLLMClient()
        print("✅ 聊天机器人已启动！")
        
        while True:
            try:
                user_input = input("\n👤 你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("👋 再见！")
                    break
                
                if not user_input:
                    continue
                
                print("🤖 思考中...")
                response = client.chat(user_input)
                print(f"🤖 助手: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 对话出错: {e}")
                
    except Exception as e:
        print(f"❌ 交互式聊天启动失败: {e}")


def main():
    """主函数"""
    print("🤖 文心大模型工具包测试")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("基础功能", test_basic_functionality),
        ("简单对话", test_simple_chat),
        ("批量处理", test_batch_processing),
        ("模型切换", test_model_switching)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        success = test_func()
        results.append((test_name, success))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 询问是否运行交互式聊天
    if any(result[1] for result in results):  # 如果有任何测试通过
        print("\n" + "=" * 50)
        choice = input("是否启动交互式聊天？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            interactive_chat()
    
    print("\n✅ 测试完成！")


if __name__ == "__main__":
    main()
