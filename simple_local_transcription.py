#!/usr/bin/env python3
"""
简单的本地音频转写方案
使用免费的临时文件分享服务上传本地文件，然后调用百度音频转写API
"""

import json
import os
import time
from typing import Any, Dict

import requests
from baidu_audio_transcription import BaiduAudioTranscription


class SimpleLocalTranscription:
    """简单的本地音频转写方案"""
    
    def __init__(self, api_key: str, secret_key: str):
        """
        初始化
        
        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.transcription_client = BaiduAudioTranscription(api_key, secret_key)
    
    def upload_to_temp_service(self, local_file_path: str) -> str:
        """
        上传文件到临时文件分享服务
        
        这里使用 file.io 作为示例（免费，24小时有效）
        你也可以使用其他类似服务：
        - transfer.sh
        - tmpfiles.org
        - 0x0.st
        
        Args:
            local_file_path: 本地文件路径
            
        Returns:
            上传后的公网URL
        """
        if not os.path.exists(local_file_path):
            raise FileNotFoundError(f"文件不存在: {local_file_path}")
        
        # 检查文件大小（500MB限制）
        file_size = os.path.getsize(local_file_path)
        if file_size > 500 * 1024 * 1024:
            raise ValueError(f"文件过大: {file_size/1024/1024:.2f}MB，最大支持500MB")
        
        print(f"正在上传文件到临时存储服务...")
        print(f"文件大小: {file_size/1024/1024:.2f}MB")
        
        try:
            # 使用 file.io 服务
            with open(local_file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post('https://file.io', files=files, timeout=300)
                response.raise_for_status()
                
                result = response.json()
                if result.get('success'):
                    url = result.get('link')
                    print(f"文件上传成功: {url}")
                    print("注意：此链接24小时后失效")
                    return url
                else:
                    raise Exception(f"上传失败: {result}")
                    
        except requests.exceptions.RequestException as e:
            print(f"上传失败，尝试备用服务...")
            # 备用方案：使用 transfer.sh
            try:
                filename = os.path.basename(local_file_path)
                with open(local_file_path, 'rb') as f:
                    response = requests.put(
                        f'https://transfer.sh/{filename}',
                        data=f,
                        timeout=300
                    )
                    response.raise_for_status()
                    url = response.text.strip()
                    print(f"文件上传成功（备用服务）: {url}")
                    print("注意：此链接14天后失效")
                    return url
            except Exception as e2:
                raise Exception(f"所有上传服务都失败了: {e}, {e2}")
    
    def transcribe_local_file(
        self, 
        local_file_path: str,
        format: str = None,
        pid: int = 80001,
        smooth_text: int = 1,
        filter_sensitive: int = 0,
        wait_for_result: bool = True
    ) -> Dict[str, Any]:
        """
        转写本地音频文件
        
        Args:
            local_file_path: 本地音频文件路径
            format: 音频格式，如果不指定则从文件扩展名推断
            pid: 语言类型 80001（中文极速版）, 80006（中文音视频字幕模型）, 1737（英文模型）
            smooth_text: 文本顺滑 0（不开启）, 1（开启）
            filter_sensitive: 敏感词过滤 0（不开启）, 1（开启）
            wait_for_result: 是否等待结果
            
        Returns:
            转写结果字典
        """
        print(f"开始处理本地音频文件: {local_file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            return {
                "success": False,
                "error": f"文件不存在: {local_file_path}",
                "error_code": -1
            }
        
        # 推断音频格式
        if format is None:
            file_ext = os.path.splitext(local_file_path)[1].lower()
            format_map = {
                '.wav': 'wav',
                '.mp3': 'mp3',
                '.pcm': 'pcm',
                '.m4a': 'm4a',
                '.amr': 'amr'
            }
            format = format_map.get(file_ext)
            if format is None:
                return {
                    "success": False,
                    "error": f"不支持的音频格式: {file_ext}",
                    "error_code": -2
                }
        
        print(f"音频格式: {format}")
        
        try:
            # 上传文件到临时存储
            speech_url = self.upload_to_temp_service(local_file_path)
            
            # 执行转写
            result = self.transcription_client.transcribe_audio_url(
                speech_url=speech_url,
                format=format,
                pid=pid,
                smooth_text=smooth_text,
                filter_sensitive=filter_sensitive,
                wait_for_result=wait_for_result
            )
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "error_code": -3
            }
    
    def save_result_to_file(self, result: Dict[str, Any], output_file: str):
        """
        保存转写结果到本地文件
        
        Args:
            result: 转写结果
            output_file: 输出文件路径
        """
        if not result.get("success"):
            print(f"转写失败，无法保存结果: {result.get('error')}")
            return
        
        # 准备保存的数据
        save_data = {
            "转写结果": result.get("result", []),
            "音频时长": f"{result.get('audio_duration', 0)/1000:.2f} 秒" if result.get('audio_duration') else "未知",
            "详细分段结果": []
        }
        
        # 处理详细分段结果
        if result.get('detailed_result'):
            for i, segment in enumerate(result['detailed_result']):
                start_time = segment.get('begin_time', 0) / 1000
                end_time = segment.get('end_time', 0) / 1000
                text = ' '.join(segment.get('res', []))
                save_data["详细分段结果"].append({
                    "序号": i + 1,
                    "开始时间": f"{start_time:.2f}s",
                    "结束时间": f"{end_time:.2f}s",
                    "文本": text
                })
        
        # 保存到文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            print(f"转写结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")


def test_your_audio_file():
    """测试你的音频文件"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv("BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo")
    
    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return
    
    # 创建转写客户端
    client = SimpleLocalTranscription(api_key, secret_key)
    
    # 你的音频文件路径
    audio_file_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"
    
    print("="*60)
    print("开始转写你的音频文件")
    print("="*60)
    
    if not os.path.exists(audio_file_path):
        print(f"错误：音频文件不存在: {audio_file_path}")
        return
    
    # 执行转写
    result = client.transcribe_local_file(
        local_file_path=audio_file_path,
        pid=80001,  # 中文极速版
        smooth_text=1,  # 开启文本顺滑
        wait_for_result=True
    )
    
    if result["success"]:
        print("\n" + "="*50)
        print("🎉 转写成功!")
        print("="*50)
        
        # 显示转写结果
        transcription_text = ' '.join(result.get('result', []))
        print(f"📝 转写结果:")
        print(f"   {transcription_text}")
        
        if result.get('audio_duration'):
            print(f"⏱️  音频时长: {result['audio_duration']/1000:.2f} 秒")
        
        # 显示详细分段结果
        if result.get('detailed_result'):
            print(f"\n📋 分段详细结果:")
            for i, segment in enumerate(result['detailed_result']):
                start_time = segment.get('begin_time', 0) / 1000
                end_time = segment.get('end_time', 0) / 1000
                text = ' '.join(segment.get('res', []))
                print(f"   [{start_time:6.2f}s - {end_time:6.2f}s]: {text}")
        
        # 保存结果到本地文件
        output_file = "转写结果.json"
        client.save_result_to_file(result, output_file)
        
        print("\n" + "="*50)
        print("✅ 转写完成！结果已保存到本地文件")
        print("="*50)
        
    else:
        print(f"\n❌ 转写失败: {result['error']}")
        if result.get('error_code'):
            print(f"   错误码: {result['error_code']}")


if __name__ == "__main__":
    test_your_audio_file()
