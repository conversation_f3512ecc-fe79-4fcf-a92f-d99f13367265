#!/usr/bin/env python3
"""
文心大模型使用示例
展示各种使用场景
"""

import os
import sys

# 添加dependencies目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wenxin_llm import WenxinLLMClient, WenxinConfig


def basic_usage_example():
    """基础使用示例"""
    print("📚 基础使用示例")
    print("-" * 30)
    
    try:
        # 1. 使用默认配置
        client = WenxinLLMClient()
        response = client.chat("你好，请介绍一下你自己")
        print(f"默认配置回复: {response[:100]}...")
        
        # 2. 指定模型
        client = WenxinLLMClient(model="ernie-4.5-21b-a3b")
        response = client.chat("什么是机器学习？")
        print(f"指定模型回复: {response[:100]}...")
        
        # 3. 自定义配置
        client = WenxinLLMClient(
            model="ernie-4.5-21b-a3b",
            api_key="your_api_key",  # 实际使用时替换
            base_url="https://aistudio.baidu.com/llm/lmapi/v3"
        )
        
    except Exception as e:
        print(f"❌ 基础使用示例失败: {e}")


def batch_processing_example():
    """批量处理示例"""
    print("\n📦 批量处理示例")
    print("-" * 30)
    
    try:
        client = WenxinLLMClient()
        
        # 批量问题
        questions = [
            "什么是Python？",
            "解释一下深度学习",
            "人工智能的应用领域有哪些？",
            "如何学习编程？"
        ]
        
        print("批量处理问题...")
        responses = client.batch_chat(questions)
        
        for i, (q, a) in enumerate(zip(questions, responses), 1):
            print(f"\n问题{i}: {q}")
            print(f"回答{i}: {a[:150]}..." if len(a) > 150 else f"回答{i}: {a}")
            
    except Exception as e:
        print(f"❌ 批量处理示例失败: {e}")


def model_switching_example():
    """模型切换示例"""
    print("\n🔄 模型切换示例")
    print("-" * 30)
    
    try:
        client = WenxinLLMClient(model="ernie-4.5-21b-a3b")
        
        test_question = "请用一句话总结人工智能的定义"
        
        # 获取可用模型
        available_models = list(WenxinConfig.list_available_models().keys())
        
        for model in available_models[:2]:  # 只测试前两个模型
            try:
                client.switch_model(model)
                response = client.chat(test_question)
                print(f"\n{model}: {response}")
            except Exception as e:
                print(f"\n{model}: ❌ 失败 - {e}")
                
    except Exception as e:
        print(f"❌ 模型切换示例失败: {e}")


def conversation_example():
    """对话示例"""
    print("\n💬 对话示例")
    print("-" * 30)
    
    try:
        client = WenxinLLMClient()
        
        # 模拟多轮对话
        conversations = [
            "你好，我想学习Python编程",
            "我是完全的初学者，应该从哪里开始？",
            "推荐一些学习资源吧",
            "大概需要多长时间能学会？"
        ]
        
        print("模拟多轮对话:")
        for i, message in enumerate(conversations, 1):
            print(f"\n👤 用户{i}: {message}")
            response = client.chat(message)
            print(f"🤖 助手{i}: {response}")
            
    except Exception as e:
        print(f"❌ 对话示例失败: {e}")


def text_analysis_example():
    """文本分析示例"""
    print("\n📝 文本分析示例")
    print("-" * 30)
    
    try:
        client = WenxinLLMClient()
        
        # 示例文本
        sample_text = """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        """
        
        # 不同的分析任务
        tasks = [
            f"请总结以下文本的主要内容：\n{sample_text}",
            f"请提取以下文本中的关键词：\n{sample_text}",
            f"请分析以下文本的情感倾向：\n{sample_text}",
            f"请将以下文本翻译成英文：\n{sample_text}"
        ]
        
        for i, task in enumerate(tasks, 1):
            print(f"\n任务{i}:")
            response = client.chat(task)
            print(f"结果: {response}")
            
    except Exception as e:
        print(f"❌ 文本分析示例失败: {e}")


def creative_writing_example():
    """创意写作示例"""
    print("\n✍️ 创意写作示例")
    print("-" * 30)
    
    try:
        client = WenxinLLMClient()
        
        # 创意写作任务
        creative_tasks = [
            "写一首关于春天的短诗",
            "编写一个关于机器人的短故事开头",
            "为一个科技公司写一句广告语",
            "描述一下未来城市的样子"
        ]
        
        for i, task in enumerate(creative_tasks, 1):
            print(f"\n创意任务{i}: {task}")
            response = client.chat(task)
            print(f"创作结果:\n{response}")
            print("-" * 20)
            
    except Exception as e:
        print(f"❌ 创意写作示例失败: {e}")


def main():
    """主函数"""
    print("🎯 文心大模型使用示例集合")
    print("=" * 50)
    
    # 检查依赖
    try:
        from langchain.chat_models import init_chat_model
    except ImportError:
        print("❌ 缺少依赖，请安装: pip install langchain")
        return
    
    # 运行各种示例
    examples = [
        ("基础使用", basic_usage_example),
        ("批量处理", batch_processing_example),
        ("模型切换", model_switching_example),
        ("对话示例", conversation_example),
        ("文本分析", text_analysis_example),
        ("创意写作", creative_writing_example)
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    print("\n选择要运行的示例 (输入数字，多个用逗号分隔，或输入'all'运行全部):")
    choice = input("请选择: ").strip()
    
    if choice.lower() == 'all':
        # 运行所有示例
        for name, func in examples:
            print(f"\n{'='*20} {name} {'='*20}")
            func()
    else:
        # 运行选定的示例
        try:
            indices = [int(x.strip()) for x in choice.split(',')]
            for idx in indices:
                if 1 <= idx <= len(examples):
                    name, func = examples[idx-1]
                    print(f"\n{'='*20} {name} {'='*20}")
                    func()
                else:
                    print(f"❌ 无效选择: {idx}")
        except ValueError:
            print("❌ 输入格式错误")
    
    print("\n✅ 示例演示完成！")


if __name__ == "__main__":
    main()
