# **赛事介绍**

语言与智能技术竞赛（LIC）是中国计算机学会（CCF）和由中国中文信息学会（CIPS）联合主办，百度公司、中国计算机学会自然语言处理专委会和中国中文信息学会评测工作委员会承办的中文NLP顶级赛事，已成为中文NLP领域内最权威、最热门的赛事之一。本届赛事将与人民日报健康客户端、智源研究院、TVB（電視廣播有限公司）合作，围绕文心大模型技术栈，开放高价值数据集，共同探索大模型的无限可能。

人民日报健康客户端以“服务+内容”定位，集健康新闻、疾病咨询、健康教育、临床学术四位一体，目前已成为国内权威的健康资讯内容聚合平台、群众看病就医服务平台、权威名医学术交流的共享平台。与百度携手打造本届LIC语言与智能技术竞赛，共同探索健康媒体与智能技术的深度融合，以数据要素驱动健康服务的智能化水平。

**本赛事需使用文心大模型4.5系列参赛（[全系列已开源，点击访问](https://aistudio.baidu.com/modelsoverview)）** **文心4.5系列开源模型共10款，涵盖了激活参数规模分别为47B和3B的混合专家(MoE)模型（最大的模型总参数量为424B),以及0.3B的稠密参数模型。**

# **比赛任务**

| 项目 | 说明 |
| --- | --- |
| **赛道名称** | 智慧医疗：从数据构建到Agent应用的全栈挑战 |
| **核心任务** | 赛道将数据工程与产品研发融为一体。参赛者需基于人民日报健康等提供的真实医疗数据，贯通**高质量数据集构建**与**智能Agent应用开发**的全流程，并选择一个方向作为主要评审侧重点。 |
| **主要环节** | 1. **数据构建：** 基于权威医疗长/短视频，进行语音转录、信息抽取和知识结构化，产出高质量的医疗问答（QA）数据集。<br>2. **应用搭建：** 利用构建的数据集对文心大模型进行微调，并开发一个可交互的智慧医疗Agent，解决真实场景问题。 |
| **提交产出** | 1. **数据集成果：** 结构化医疗QA数据集及完整的处理流程文档。<br>2. **应用成果：** 智慧医疗Agent应用Demo及技术方案说明文档。 |
| **评测机制** | 最终成绩由**评估集自动评测(40%)与现场答辩分(60%)组成。专业评审将根据团队选择的侧重点进行：若侧重数据构建**，则主要评估数据集质量；若侧重**应用搭建**，则主要评估应用产品的完整性与技术深度。 |
| **赛题亮点** | 体验从原始数据到AI产品的全链路开发；接触业界稀缺、高信噪比的真实医疗语料；将数据工程能力与大模型应用能力深度结合，全面锻炼解决复杂行业问题的综合能力。 |

# 数据集介绍

重要说明：参赛团队需在**7月14日**及之前提交技术方案，提交内容必须包含团队介绍、数据处理思路、项目名称、产品设计图、技术实现路径等，以PDF文档格式提交至官方邮箱**********************，命名为【赛道-团队名-作品名称】，通过筛选的团队可获取全量数据集。 本赛道开放来自人民日报健康客户端、百度健康等渠道的四大类核心医疗数据集。这些数据覆盖了从权威专家长视频讲座、轻量级科普短视频、多模态图文内容到结构化疾病知识库等多种类型和场景。初赛阶段将开放各类数据的部分示例，帮助参赛队伍熟悉数据格式与任务方向，以下为部分任务参考示例，大家可**自由发挥创意，设定开发方向**。

**官方示例数据集链接：** [**LIC 2025 赛道一：智慧医疗数据集（飞桨AI Studio星河社区）**](https://aistudio.baidu.com/datasetdetail/345031)

1. **“人民名医”专家长视频**
    - **数据描述：** 由国内顶级权威专家主讲的长视频讲座（约1小时/期），包含系统、深入的疾病诊疗知识。
    - **核心任务：** 将长视频的**语音转录为文本**，并从中提炼、构建**高质量、结构化的QA问答对**。
    - **应用价值：** 构建体系化的高质量医疗对话数据集，用于**微调领域大模型**；将专家知识提炼为样本，作为**预训练数据增强**。
    - **初赛示例：** 提供 `人民名医.xlsx` 文件，内含5-10个专家讲座视频的观看链接及基础信息。
2. **“大夫说”科普短视频**
    - **数据描述：** 面向公众的轻量级科普短视频（约3分钟/期）。
    - **核心任务：** 进行**关键信息提取**，快速生成**标准化的QA问答对**。
    - **应用价值：** 快速生成轻量级科普语料，适配移动端健康助手场景，可作为高质量的**RAG知识库**。
    - **初赛示例：** 提供 `大夫说.xls` 文件，内含5-10个短视频的观看链接及基础信息。
3. **“健康号”多模态内容**
    - **数据描述：** 包含海量（总量10万+）的**图文和视频**内容。
    - **核心任务：** 进行**多模态数据融合与对齐**，构建跨模态数据集。
    - **应用价值：** 用于训练**医疗多模态大模型**的基座能力，或构建**多模态RAG知识库**，实现跨模态搜索、AIGC科普内容智能生成等。
    - **初赛示例：** 提供一个“健康号”文件夹，其中包含**数个.mp4格式的小视频文件**作为样例。
4. **结构化疾病库**
    - **数据描述：** 包含1450个结构化的疾病条目。
    - **核心任务：** 基于条目进行**知识图谱构建**，形成**实体关系三元组**。
    - **应用价值：** 将疾病关联关系数字化，支撑**诊断辅助系统**的开发或作为高质量的**患者教育知识库**。
    - **初赛示例：** 提供 `查疾病.xls` 文件，内含5-10个疾病条目的示例数据。

## **任务要求**

- **指定大模型技术栈：** 百度文心大模型4.5开源系列（[全系列已开源，点击访问](https://aistudio.baidu.com/modelsoverview)）
- **指定开发与提交平台：** 百度飞桨AI Studio星河社区。
- **数据集合规性：** 参赛者使用或构建的任何数据集，均需保证来源合法、合规，不侵犯任何第三方权益。