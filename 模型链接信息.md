
# 文心大模型
```py
# -*- coding: utf-8 -*-
# pip install openai
from openai import OpenAI

client = OpenAI(
    api_key="450803ef6babe9f237b3e393230d3f8c6645b7fe",  # Access Token属于个人账户的重要隐私信息，请谨慎管理，切忌随意对外公开,
    base_url="https://aistudio.baidu.com/llm/lmapi/v3",  # aistudio 大模型 api 服务域名
)

chat_completion = client.chat.completions.create(
    model="ernie-4.5-turbo-vl-preview",
    messages=[
    {
        "role": "user",
        "content": "在这里输入你的问题"
    }
],
    stream=True,
    extra_body={
        "penalty_score": 1
    },
    max_completion_tokens=2000,
    temperature=0.2,
    top_p=0.8,
    frequency_penalty=0,
    presence_penalty=0
)

for chunk in chat_completion:
    if hasattr(chunk.choices[0].delta, "reasoning_content") and chunk.choices[0].delta.reasoning_content:
        print(chunk.choices[0].delta.reasoning_content, end="", flush=True)
    else:
        print(chunk.choices[0].delta.content, end="", flush=True)
```
