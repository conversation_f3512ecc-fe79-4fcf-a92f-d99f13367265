#!/usr/bin/env python3
"""
完整的音频转写解决方案
支持本地文件上传到临时存储，然后使用百度音频文件转写API
"""

import json
import os
import tempfile
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin
import shutil

import requests
from baidu_audio_transcription import BaiduAudioTranscription


class CompleteAudioTranscription:
    """完整的音频转写解决方案"""
    
    def __init__(self, api_key: str, secret_key: str):
        """
        初始化
        
        Args:
            api_key: 百度智能云API Key
            secret_key: 百度智能云Secret Key
        """
        self.transcription_client = BaiduAudioTranscription(api_key, secret_key)
        
    def upload_to_temp_storage(self, local_file_path: str) -> Optional[str]:
        """
        上传文件到临时存储服务
        
        注意：这里使用一个简单的文件服务器示例
        在实际使用中，你需要：
        1. 使用百度云对象存储BOS
        2. 或其他云存储服务（阿里云OSS、腾讯云COS等）
        3. 或自建文件服务器
        
        Args:
            local_file_path: 本地文件路径
            
        Returns:
            上传后的公网URL，失败返回None
        """
        # 这里是一个示例实现
        # 实际使用时需要替换为真实的文件上传服务
        
        print("注意：当前使用模拟上传，实际使用需要配置真实的云存储服务")
        
        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            print(f"文件不存在: {local_file_path}")
            return None
        
        # 检查文件大小（500MB限制）
        file_size = os.path.getsize(local_file_path)
        if file_size > 500 * 1024 * 1024:
            print(f"文件过大: {file_size/1024/1024:.2f}MB，最大支持500MB")
            return None
        
        print(f"文件大小: {file_size/1024/1024:.2f}MB")
        
        # 这里返回一个示例URL
        # 实际实现中，你需要：
        # 1. 上传文件到云存储
        # 2. 返回公网可访问的URL
        
        # 示例：使用百度提供的测试文件
        return "https://platform.bj.bcebos.com/sdk/asr/asr_doc/doc_download_files/16k.wav"
    
    def transcribe_local_file(
        self, 
        local_file_path: str,
        format: str = None,
        pid: int = 80001,
        smooth_text: int = 1,
        filter_sensitive: int = 0,
        wait_for_result: bool = True
    ) -> Dict[str, Any]:
        """
        转写本地音频文件
        
        Args:
            local_file_path: 本地音频文件路径
            format: 音频格式，如果不指定则从文件扩展名推断
            pid: 语言类型
            smooth_text: 文本顺滑
            filter_sensitive: 敏感词过滤
            wait_for_result: 是否等待结果
            
        Returns:
            转写结果字典
        """
        print(f"开始处理本地音频文件: {local_file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(local_file_path):
            return {
                "success": False,
                "error": f"文件不存在: {local_file_path}",
                "error_code": -1
            }
        
        # 推断音频格式
        if format is None:
            file_ext = os.path.splitext(local_file_path)[1].lower()
            format_map = {
                '.wav': 'wav',
                '.mp3': 'mp3',
                '.pcm': 'pcm',
                '.m4a': 'm4a',
                '.amr': 'amr'
            }
            format = format_map.get(file_ext)
            if format is None:
                return {
                    "success": False,
                    "error": f"不支持的音频格式: {file_ext}",
                    "error_code": -2
                }
        
        print(f"音频格式: {format}")
        
        # 上传文件到云存储
        print("正在上传文件到云存储...")
        speech_url = self.upload_to_temp_storage(local_file_path)
        
        if speech_url is None:
            return {
                "success": False,
                "error": "文件上传失败",
                "error_code": -3
            }
        
        print(f"文件上传成功: {speech_url}")
        
        # 执行转写
        return self.transcription_client.transcribe_audio_url(
            speech_url=speech_url,
            format=format,
            pid=pid,
            smooth_text=smooth_text,
            filter_sensitive=filter_sensitive,
            wait_for_result=wait_for_result
        )
    
    def batch_transcribe_files(
        self, 
        file_paths: List[str],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        批量转写多个音频文件
        
        Args:
            file_paths: 音频文件路径列表
            **kwargs: 其他参数传递给transcribe_local_file
            
        Returns:
            转写结果列表
        """
        results = []
        
        for i, file_path in enumerate(file_paths):
            print(f"\n处理第 {i+1}/{len(file_paths)} 个文件: {file_path}")
            
            result = self.transcribe_local_file(file_path, **kwargs)
            result["file_path"] = file_path
            results.append(result)
            
            # 如果失败，继续处理下一个文件
            if not result["success"]:
                print(f"文件 {file_path} 处理失败: {result['error']}")
            else:
                print(f"文件 {file_path} 处理成功")
        
        return results


def setup_cloud_storage_guide():
    """显示云存储配置指南"""
    print("\n" + "="*60)
    print("云存储配置指南")
    print("="*60)
    print("要使用音频文件转写API，需要将音频文件上传到云存储获得公网URL。")
    print("推荐的云存储服务：")
    print()
    print("1. 百度云对象存储BOS")
    print("   - 官网：https://cloud.baidu.com/product/bos.html")
    print("   - 优势：与百度AI服务集成度高")
    print()
    print("2. 阿里云对象存储OSS")
    print("   - 官网：https://www.aliyun.com/product/oss")
    print()
    print("3. 腾讯云对象存储COS")
    print("   - 官网：https://cloud.tencent.com/product/cos")
    print()
    print("配置步骤：")
    print("1. 注册云存储服务")
    print("2. 创建存储桶（Bucket）")
    print("3. 设置公网访问权限")
    print("4. 修改 upload_to_temp_storage 方法实现真实上传")
    print("="*60)


def test_with_local_file():
    """测试本地文件转写"""
    # 从环境变量获取API密钥
    api_key = os.getenv("BAIDU_API_KEY", default="tglobxgL5s1lfpv5wGmdTlZD")
    secret_key = os.getenv("BAIDU_SECRET_KEY", default="7Zuk2oXoVcTwEFLYXy8YbKSOiVY5PjWo")
    
    if not api_key or not secret_key:
        print("错误：请先设置环境变量 BAIDU_API_KEY 和 BAIDU_SECRET_KEY")
        return
    
    # 创建完整转写客户端
    client = CompleteAudioTranscription(api_key, secret_key)
    
    # 测试音频文件路径
    test_audio_path = "/Volumes/Data/00-games/lic2025-med-agent-triage/data/初赛数据示例/健康号/1c826bee2fb84eb9888963c0fcb2f28f.wav"
    
    print("开始本地文件转写测试...")
    
    if os.path.exists(test_audio_path):
        # 执行转写
        result = client.transcribe_local_file(
            local_file_path=test_audio_path,
            pid=80001,  # 中文极速版
            smooth_text=1,  # 开启文本顺滑
            wait_for_result=True
        )
        
        if result["success"]:
            print("\n" + "="*50)
            print("转写成功!")
            print(f"转写结果: {result['result']}")
            if result.get('audio_duration'):
                print(f"音频时长: {result['audio_duration']/1000:.2f} 秒")
            
            # 显示详细分段结果
            if result.get('detailed_result'):
                print("\n分段详细结果:")
                for i, segment in enumerate(result['detailed_result']):
                    start_time = segment.get('begin_time', 0) / 1000
                    end_time = segment.get('end_time', 0) / 1000
                    text = ' '.join(segment.get('res', []))
                    print(f"  [{start_time:.2f}s - {end_time:.2f}s]: {text}")
            
            print("="*50)
        else:
            print(f"\n转写失败: {result['error']} (错误码: {result.get('error_code')})")
    else:
        print(f"测试音频文件不存在: {test_audio_path}")
        print("请确保文件路径正确")
    
    # 显示云存储配置指南
    setup_cloud_storage_guide()


if __name__ == "__main__":
    test_with_local_file()
