# 文心大模型工具包

基于LangChain的文心大模型封装工具包，提供简单易用的API接口。

## 🚀 特性

- **简单易用**：封装了复杂的配置，提供简洁的API
- **多模型支持**：支持多个文心大模型版本
- **批量处理**：支持批量对话处理
- **模型切换**：运行时动态切换模型
- **配置管理**：统一的配置管理
- **错误处理**：完善的错误处理机制

## 📦 安装

```bash
pip install langchain
```

## 🔑 配置

设置环境变量（可选）：

```bash
export WENXIN_API_KEY='你的API密钥'
export WENXIN_BASE_URL='https://aistudio.baidu.com/llm/lmapi/v3'
```

## 🎯 快速开始

### 基础使用

```python
from wenxin_llm import WenxinLLMClient

# 使用默认配置
client = WenxinLLMClient()
response = client.chat("你好，请介绍一下你自己")
print(response)
```

### 指定模型

```python
# 指定特定模型
client = WenxinLLMClient(model="ernie-4.5-21b-a3b")
response = client.chat("什么是人工智能？")
print(response)
```

### 自定义配置

```python
# 自定义配置
client = WenxinLLMClient(
    model="ernie-4.5-21b-a3b",
    api_key="your_api_key",
    base_url="https://aistudio.baidu.com/llm/lmapi/v3"
)
```

## 📋 支持的模型

| 模型名称 | 描述 | 最大Tokens | 流式支持 |
|----------|------|------------|----------|
| ernie-4.5-21b-a3b | ERNIE 4.5 21B A3B | 8192 | ✅ |
| ernie-4.0-8k | ERNIE 4.0 8K | 8192 | ✅ |
| ernie-3.5 | ERNIE 3.5 | 4096 | ✅ |

## 🔧 主要功能

### 1. 单轮对话

```python
client = WenxinLLMClient()
response = client.chat("解释一下机器学习")
print(response)
```

### 2. 批量处理

```python
questions = [
    "什么是Python？",
    "解释一下深度学习",
    "人工智能的应用领域有哪些？"
]

responses = client.batch_chat(questions)
for q, a in zip(questions, responses):
    print(f"Q: {q}")
    print(f"A: {a}\n")
```

### 3. 模型切换

```python
client = WenxinLLMClient(model="ernie-4.5-21b-a3b")

# 切换到其他模型
client.switch_model("ernie-3.5")
response = client.chat("你好")
```

### 4. 获取模型信息

```python
# 获取当前模型信息
model_info = client.get_model_info()
print(f"模型名称: {model_info['name']}")
print(f"描述: {model_info['description']}")
print(f"最大tokens: {model_info['max_tokens']}")

# 获取配置信息
config = client.get_config()
print(f"当前配置: {config}")
```

## 📁 文件结构

```
dependencies/wenxin_llm/
├── __init__.py          # 包初始化
├── config.py            # 配置管理
├── wenxin_client.py     # 主要客户端
├── demo.py              # 基础演示
├── examples.py          # 使用示例
└── README.md            # 说明文档
```

## 🧪 运行示例

### 基础演示

```bash
cd dependencies/wenxin_llm
python demo.py
```

### 使用示例

```bash
python examples.py
```

### 测试客户端

```bash
python wenxin_client.py
```

## 📝 使用场景

### 1. 文本分析

```python
client = WenxinLLMClient()

text = "这是一段需要分析的文本..."
summary = client.chat(f"请总结以下文本：\n{text}")
keywords = client.chat(f"请提取关键词：\n{text}")
sentiment = client.chat(f"请分析情感倾向：\n{text}")
```

### 2. 创意写作

```python
client = WenxinLLMClient()

poem = client.chat("写一首关于春天的诗")
story = client.chat("编写一个科幻故事的开头")
slogan = client.chat("为AI公司写一句广告语")
```

### 3. 问答系统

```python
client = WenxinLLMClient()

def qa_system():
    while True:
        question = input("请输入问题 (输入'quit'退出): ")
        if question.lower() == 'quit':
            break
        
        answer = client.chat(question)
        print(f"回答: {answer}\n")

qa_system()
```

### 4. 多轮对话

```python
client = WenxinLLMClient()

conversation_history = []

def chat_with_context(message):
    # 构建上下文
    context = "\n".join(conversation_history[-5:])  # 保留最近5轮对话
    full_message = f"上下文：{context}\n\n当前问题：{message}"
    
    response = client.chat(full_message)
    
    # 更新对话历史
    conversation_history.append(f"用户: {message}")
    conversation_history.append(f"助手: {response}")
    
    return response
```

## ⚠️ 注意事项

1. **API密钥**：确保使用有效的API密钥
2. **网络连接**：需要稳定的网络连接
3. **依赖安装**：确保安装了langchain
4. **模型限制**：注意各模型的token限制
5. **错误处理**：建议添加适当的错误处理

## 🔗 相关链接

- [百度AI Studio](https://aistudio.baidu.com/)
- [LangChain文档](https://python.langchain.com/)
- [文心大模型文档](https://cloud.baidu.com/doc/WENXINWORKSHOP/index.html)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请查看：
1. README文档
2. 示例代码
3. 错误日志
4. 官方文档
