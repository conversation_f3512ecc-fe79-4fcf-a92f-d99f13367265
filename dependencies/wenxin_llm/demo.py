#!/usr/bin/env python3
"""
文心大模型连接演示
基于原始demo的整理版本
"""

import os
import sys

# 添加dependencies目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wenxin_llm import WenxinLLMClient, WenxinConfig

try:
    from langchain.chat_models import init_chat_model
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


def original_demo():
    """原始demo的复现"""
    print("🔄 原始demo复现")
    print("-" * 30)
    
    if not LANGCHAIN_AVAILABLE:
        print("❌ 需要安装langchain: pip install langchain")
        return
    
    # 原始配置
    llm_config = {
        "model": "ernie-4.5-21b-a3b",
        "base_url": "https://aistudio.baidu.com/llm/lmapi/v3",
        "api_key": "450803ef6babe9f237b3e393230d3f8c6645b7fe",
        "model_provider": "openai",
    }
    
    try:
        # 使用原始方式
        llm = init_chat_model(**llm_config)
        res = llm.invoke("你好")
        print(f"原始方式结果: {res}")
    except Exception as e:
        print(f"原始方式失败: {e}")


def enhanced_demo():
    """增强版demo"""
    print("\n🚀 增强版demo")
    print("-" * 30)
    
    try:
        # 使用封装的客户端
        client = WenxinLLMClient(model="ernie-4.5-21b-a3b")
        
        # 基础对话
        response = client.chat("你好")
        print(f"增强版结果: {response}")
        
        # 显示模型信息
        model_info = client.get_model_info()
        print(f"\n📋 模型信息:")
        print(f"   名称: {model_info.get('name')}")
        print(f"   描述: {model_info.get('description')}")
        print(f"   最大tokens: {model_info.get('max_tokens')}")
        
    except Exception as e:
        print(f"增强版失败: {e}")


def interactive_demo():
    """交互式demo"""
    print("\n💬 交互式demo")
    print("-" * 30)
    print("输入 'quit' 或 'exit' 退出")
    
    try:
        client = WenxinLLMClient()
        
        while True:
            user_input = input("\n用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            try:
                response = client.chat(user_input)
                print(f"助手: {response}")
            except Exception as e:
                print(f"❌ 对话失败: {e}")
                
    except KeyboardInterrupt:
        print("\n👋 用户中断，再见！")
    except Exception as e:
        print(f"❌ 交互式demo失败: {e}")


def model_comparison_demo():
    """模型对比demo"""
    print("\n🔍 模型对比demo")
    print("-" * 30)
    
    # 获取可用模型
    available_models = WenxinConfig.list_available_models()
    print(f"可用模型: {list(available_models.keys())}")
    
    test_question = "请用一句话解释什么是人工智能"
    print(f"\n测试问题: {test_question}")
    
    for model_name in available_models.keys():
        try:
            print(f"\n🤖 {model_name}:")
            client = WenxinLLMClient(model=model_name)
            response = client.chat(test_question)
            print(f"   回答: {response[:200]}..." if len(response) > 200 else f"   回答: {response}")
        except Exception as e:
            print(f"   ❌ 失败: {e}")


def main():
    """主函数"""
    print("🤖 文心大模型连接演示")
    print("=" * 50)
    
    # 检查依赖
    if not LANGCHAIN_AVAILABLE:
        print("❌ 缺少依赖，请安装: pip install langchain")
        return
    
    # 运行各种demo
    original_demo()
    enhanced_demo()
    
    # 询问是否运行交互式demo
    print("\n" + "=" * 50)
    choice = input("是否运行交互式demo？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        interactive_demo()
    
    # 询问是否运行模型对比demo
    choice = input("是否运行模型对比demo？(y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        model_comparison_demo()
    
    print("\n✅ 演示完成！")


if __name__ == "__main__":
    main()
